{% extends "base.html" %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Add New Supplier</h6>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                {{ form|crispy }}
                <button type="submit" class="btn btn-primary">Add Supplier</button>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4 mt-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Existing Suppliers</h6>
        </div>
        <div class="card-body">
            {% if suppliers %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Contact Person</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>City</th>
                            <th>Active</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for supplier in suppliers %}
                        <tr>
                            <td>{{ supplier.name }}</td>
                            <td>{{ supplier.contact_person|default:"N/A" }}</td>
                            <td>{{ supplier.phone_number }}</td>
                            <td>{{ supplier.email|default:"N/A" }}</td>
                            <td>{{ supplier.city }}</td>
                            <td>
                                {% if supplier.is_active %}
                                    <span class="badge bg-success">Yes</span>
                                {% else %}
                                    <span class="badge bg-danger">No</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'pharmacy:edit_supplier' supplier.id %}" class="btn btn-info btn-sm">Edit</a>
                                <a href="{% url 'pharmacy:delete_supplier' supplier.id %}" class="btn btn-danger btn-sm">Delete</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p>No suppliers found.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}