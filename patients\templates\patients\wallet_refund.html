{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  {{ title }} | {{ block.super }}
{% endblock title %}

{% block page_title %}
  {{ title }}
{% endblock page_title %}

{% block breadcrumbs %}
  <li class="breadcrumb-item"><a href="{% url 'patients:list' %}">Patients</a></li>
  <li class="breadcrumb-item"><a href="{% url 'patients:detail' patient.id %}">{{ patient.get_full_name }}</a></li>
  <li class="breadcrumb-item"><a href="{% url 'patients:wallet_dashboard' patient.id %}">Wallet</a></li>
  <li class="breadcrumb-item active" aria-current="page">Process Refund</li>
{% endblock breadcrumbs %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Process Refund to Patient Wallet</h6>
            </div>
            <div class="card-body">
                <!-- Patient Info -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-user"></i> Patient Information</h6>
                    <p class="mb-1"><strong>Name:</strong> {{ patient.get_full_name }}</p>
                    <p class="mb-1"><strong>Patient ID:</strong> {{ patient.patient_id }}</p>
                    <p class="mb-0"><strong>Current Wallet Balance:</strong> <span class="text-success font-weight-bold">₦{{ wallet.balance|floatformat:2 }}</span></p>
                </div>

                <!-- Refund Form -->
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.amount.label_tag }}
                                {{ form.amount }}
                                {% if form.amount.errors %}
                                    <div class="text-danger">
                                        {% for error in form.amount.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Enter the amount to refund to the patient
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.reference_invoice.label_tag }}
                                {{ form.reference_invoice }}
                                {% if form.reference_invoice.errors %}
                                    <div class="text-danger">
                                        {% for error in form.reference_invoice.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Optional: Related invoice number
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        {{ form.reason.label_tag }}
                        {{ form.reason }}
                        {% if form.reason.errors %}
                            <div class="text-danger">
                                {% for error in form.reason.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            Provide a detailed reason for this refund
                        </small>
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                <p class="mb-0">{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Refund Summary (Hidden by default, shown via JavaScript) -->
                    <div id="refund-summary" class="alert alert-success" style="display: none;">
                        <h6><i class="fas fa-undo"></i> Refund Summary</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Patient:</strong> {{ patient.get_full_name }}<br>
                                <strong>Current Balance:</strong> ₦{{ wallet.balance|floatformat:2 }}<br>
                                <strong>Balance After Refund:</strong> <span id="balance-after">₦0.00</span>
                            </div>
                            <div class="col-md-6">
                                <strong>Refund Amount:</strong> <span id="refund-amount">₦0.00</span><br>
                                <strong>Reference Invoice:</strong> <span id="refund-invoice">-</span><br>
                                <strong>Reason:</strong> <span id="refund-reason">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="form-group mt-4">
                        <button type="submit" class="btn btn-primary" id="refund-btn" disabled>
                            <i class="fas fa-undo"></i> Process Refund
                        </button>
                        <a href="{% url 'patients:wallet_dashboard' patient.id %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Wallet Summary -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Wallet Summary</h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-wallet fa-3x text-gray-300 mb-3"></i>
                    <h4 class="text-primary">₦{{ wallet.balance|floatformat:2 }}</h4>
                    <p class="text-muted">Current Balance</p>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="text-success">
                            <i class="fas fa-arrow-up"></i>
                            <div class="font-weight-bold">₦{{ wallet.get_total_credits|floatformat:2 }}</div>
                            <small>Total Credits</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-danger">
                            <i class="fas fa-arrow-down"></i>
                            <div class="font-weight-bold">₦{{ wallet.get_total_debits|floatformat:2 }}</div>
                            <small>Total Debits</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'patients:add_funds_to_wallet' patient.id %}" class="btn btn-success btn-sm">
                        <i class="fas fa-plus"></i> Add Funds
                    </a>
                    <a href="{% url 'patients:wallet_withdrawal' patient.id %}" class="btn btn-warning btn-sm">
                        <i class="fas fa-minus"></i> Withdraw Funds
                    </a>
                    <a href="{% url 'patients:wallet_transfer' patient.id %}" class="btn btn-info btn-sm">
                        <i class="fas fa-exchange-alt"></i> Transfer Funds
                    </a>
                    <a href="{% url 'patients:wallet_adjustment' patient.id %}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-edit"></i> Make Adjustment
                    </a>
                    <a href="{% url 'patients:wallet_transactions' patient.id %}" class="btn btn-dark btn-sm">
                        <i class="fas fa-list"></i> View Transactions
                    </a>
                </div>
            </div>
        </div>

        <!-- Refund Guidelines -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">Refund Guidelines</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check-circle text-success"></i>
                        <small>Refunds are processed immediately</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-shield-alt text-primary"></i>
                        <small>All refunds are secure and audited</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-file-invoice text-info"></i>
                        <small>Link to invoice for better tracking</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-comment text-warning"></i>
                        <small>Provide clear reason for refund</small>
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-receipt text-success"></i>
                        <small>Patient will see refund in transaction history</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for real-time refund validation and summary -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('id_amount');
    const reasonInput = document.getElementById('id_reason');
    const invoiceInput = document.getElementById('id_reference_invoice');
    const refundSummary = document.getElementById('refund-summary');
    const refundBtn = document.getElementById('refund-btn');
    const currentBalance = {{ wallet.balance }};
    
    function updateRefundSummary() {
        const amount = parseFloat(amountInput.value) || 0;
        const reason = reasonInput.value.trim();
        const invoice = invoiceInput.value.trim() || '-';
        
        if (amount > 0 && reason) {
            // Show summary
            refundSummary.style.display = 'block';
            
            // Update summary values
            document.getElementById('balance-after').textContent = `₦${(currentBalance + amount).toFixed(2)}`;
            document.getElementById('refund-amount').textContent = `₦${amount.toFixed(2)}`;
            document.getElementById('refund-invoice').textContent = invoice;
            document.getElementById('refund-reason').textContent = reason.substring(0, 50) + (reason.length > 50 ? '...' : '');
            
            // Enable refund button
            refundBtn.disabled = false;
            refundBtn.className = 'btn btn-primary';
        } else {
            refundSummary.style.display = 'none';
            refundBtn.disabled = true;
            refundBtn.className = 'btn btn-secondary';
        }
    }
    
    function validateAmount() {
        const amount = parseFloat(amountInput.value) || 0;
        
        // Remove existing feedback
        const existingFeedback = amountInput.parentNode.querySelector('.balance-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }
        
        // Add new feedback
        if (amount > 0) {
            const feedback = document.createElement('small');
            feedback.className = 'form-text balance-feedback text-info';
            feedback.innerHTML = `<i class="fas fa-info-circle"></i> Balance after refund: ₦${(currentBalance + amount).toFixed(2)}`;
            amountInput.parentNode.appendChild(feedback);
        }
    }
    
    // Event listeners
    if (amountInput) {
        amountInput.addEventListener('input', function() {
            validateAmount();
            updateRefundSummary();
        });
    }
    
    if (reasonInput) {
        reasonInput.addEventListener('input', updateRefundSummary);
    }
    
    if (invoiceInput) {
        invoiceInput.addEventListener('input', updateRefundSummary);
    }
});
</script>
{% endblock content %}
