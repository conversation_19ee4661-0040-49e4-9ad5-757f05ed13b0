{% extends 'core/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h2 class="my-4">{{ title }}</h2>

    <!-- Search and Filter Form -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-search me-1"></i>
            Search and Filter
        </div>
        <div class="card-body">
            <form method="get" action="">
                <div class="row g-3">
                    <div class="col-md-4">
                        {{ form.medication_name.label_tag }}
                        {{ form.medication_name }}
                    </div>
                    <div class="col-md-4">
                        {{ form.patient_name.label_tag }}
                        {{ form.patient_name }}
                    </div>
                    <div class="col-md-4">
                        {{ form.dispensed_by.label_tag }}
                        {{ form.dispensed_by }}
                    </div>
                    <div class="col-md-3">
                        {{ form.date_from.label_tag }}
                        {{ form.date_from }}
                    </div>
                    <div class="col-md-3">
                        {{ form.date_to.label_tag }}
                        {{ form.date_to }}
                    </div>
                    <div class="col-md-3">
                        {{ form.category.label_tag }}
                        {{ form.category }}
                    </div>
                    <div class="col-md-3">
                        {{ form.prescription_type.label_tag }}
                        {{ form.prescription_type }}
                    </div>
                    <div class="col-md-2">
                        {{ form.min_quantity.label_tag }}
                        {{ form.min_quantity }}
                    </div>
                    <div class="col-md-2">
                        {{ form.max_quantity.label_tag }}
                        {{ form.max_quantity }}
                    </div>
                    <div class="col-md-2 align-self-end">
                        <button type="submit" class="btn btn-primary w-100">Search</button>
                    </div>
                    <div class="col-md-2 align-self-end">
                        <a href="{% url 'pharmacy:dispensed_items_tracker' %}" class="btn btn-secondary w-100">Reset</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <h5 class="card-title">Today's Dispensed Items</h5>
                    <p class="card-text">{{ stats.today.total_items|default:0 }} items</p>
                    <p class="card-text">Value: ${{ stats.today.total_value|default:"0.00" }}</p>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <h5 class="card-title">This Week's Dispensed Items</h5>
                    <p class="card-text">{{ stats.this_week.total_items|default:0 }} items</p>
                    <p class="card-text">Value: ${{ stats.this_week.total_value|default:"0.00" }}</p>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <h5 class="card-title">This Month's Dispensed Items</h5>
                    <p class="card-text">{{ stats.this_month.total_items|default:0 }} items</p>
                    <p class="card-text">Value: ${{ stats.this_month.total_value|default:"0.00" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Dispensed Items Table -->
    <div class="card">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Dispensed Items
            <a href="{% url 'pharmacy:dispensed_items_export' %}?{{ request.GET.urlencode }}" class="btn btn-sm btn-success float-end">
                <i class="fas fa-file-csv me-1"></i> Export to CSV
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>Log ID</th>
                            <th>Dispensed Date</th>
                            <th>Medication</th>
                            <th>Category</th>
                            <th>Quantity</th>
                            <th>Unit Price</th>
                            <th>Total Price</th>
                            <th>Patient</th>
                            <th>Dispensed By</th>
                            <th>Dispensary</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in page_obj %}
                        <tr>
                            <td>{{ log.id }}</td>
                            <td>{{ log.dispensed_date|date:"Y-m-d H:i" }}</td>
                            <td>{{ log.prescription_item.medication.name }}</td>
                            <td>{{ log.prescription_item.medication.category.name|default:"N/A" }}</td>
                            <td>{{ log.dispensed_quantity }}</td>
                            <td>${{ log.unit_price_at_dispense }}</td>
                            <td>${{ log.total_price_for_this_log }}</td>
                            <td>{{ log.prescription_item.prescription.patient.get_full_name }}</td>
                            <td>{{ log.dispensed_by.get_full_name|default:"N/A" }}</td>
                            <td>{{ log.dispensary.name|default:"N/A" }}</td>
                            <td>
                                <a href="{% url 'pharmacy:dispensed_item_detail' log.id %}" class="btn btn-sm btn-info">Details</a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="11" class="text-center">No dispensed items found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item"><a class="page-link" href="?page=1&{{ request.GET.urlencode }}">&laquo; first</a></li>
                    <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}">previous</a></li>
                    {% endif %}

                    <li class="page-item disabled"><span class="page-link">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}.</span></li>

                    {% if page_obj.has_next %}
                    <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}">next</a></li>
                    <li class="page-item"><a class="page-link" href="?page={{ page_obj.paginator.num_pages }}&{{ request.GET.urlencode }}">last &raquo;</a></li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

{% block extra_js %}
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script>
$(function() {
    $("#id_medication_name").autocomplete({
        source: "{% url 'pharmacy:medication_autocomplete' %}",
        minLength: 2
    });
});
</script>
{% endblock %}
{% endblock %}
