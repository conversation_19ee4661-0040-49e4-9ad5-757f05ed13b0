{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:dashboard' %}">Dashboard</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ title }}</li>
{% endblock breadcrumbs %}

{% block content %}
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{{ title }}</h6>
        <form class="d-none d-sm-inline-block form-inline mr-auto my-2 my-md-0 mw-100 navbar-search" method="GET">
            <div class="input-group">
                <input type="text" class="form-control bg-light border-0 small" placeholder="Search for NHIA patient..." aria-label="Search"
                       aria-describedby="basic-addon2" name="search" value="{{ search_query }}">
                <div class="input-group-append">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search fa-sm"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Patient Name</th>
                        <th>Patient ID</th>
                        <th>NHIA Reg. No.</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Date Registered</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for nhia_patient in page_obj %}
                    <tr>
                        <td><a href="{% url 'patients:detail' nhia_patient.patient.id %}">{{ nhia_patient.patient.get_full_name }}</a></td>
                        <td>{{ nhia_patient.patient.patient_id }}</td>
                        <td>{{ nhia_patient.nhia_reg_number }}</td>
                        <td><span class="badge bg-success">NHIA</span></td>
                        <td>
                            {% if nhia_patient.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </td>
                        <td>{{ nhia_patient.date_registered|date:"Y-m-d H:i" }}</td>
                        <td>
                            <a href="{% url 'patients:edit_nhia_patient' nhia_patient.patient.id %}" class="btn btn-sm btn-primary">Edit</a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7">No NHIA patients found.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <nav aria-label="Page navigation example">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Previous</a></li>
                {% endif %}

                {% for i in page_obj.paginator.page_range %}
                    {% if page_obj.number == i %}
                        <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                    {% else %}
                        <li class="page-item"><a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a></li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Next</a></li>
                {% endif %}
            </ul>
        </nav>

    </div>
</div>
{% endblock %}