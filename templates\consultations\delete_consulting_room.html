{% extends 'base.html' %}

{% block title %}Delete Consulting Room - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Delete Consulting Room</h1>
        <a href="{% url 'consultations:consulting_room_list' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>

    <div class="row">
        <div class="col-lg-6 mx-auto">
            <div class="card shadow mb-4 border-left-danger">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">Confirm Deletion</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Are you sure you want to delete this consulting room? This action cannot be undone.
                    </div>
                    
                    <div class="mb-4">
                        <h5 class="font-weight-bold">Room Details:</h5>
                        <p><strong>Room Number:</strong> {{ consulting_room.room_number }}</p>
                        <p><strong>Floor:</strong> {{ consulting_room.floor }}</p>
                        <p><strong>Department:</strong> {{ consulting_room.department.name|default:"Unassigned" }}</p>
                        <p><strong>Description:</strong> {{ consulting_room.description|default:"-" }}</p>
                        <p><strong>Status:</strong> 
                            {% if consulting_room.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </p>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'consultations:consulting_room_list' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i> Delete Room
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
