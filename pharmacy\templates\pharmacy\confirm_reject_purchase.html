{% extends "base.html" %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Reject Purchase #{{ purchase.invoice_number }}</h6>
        </div>
        <div class="card-body">
            <p>Are you sure you want to reject this purchase?</p>
            <form method="post">
                {% csrf_token %}
                <div class="form-group">
                    <label for="approval_notes">Reason for Rejection (Optional):</label>
                    <textarea name="approval_notes" id="approval_notes" class="form-control"></textarea>
                </div>
                <button type="submit" class="btn btn-danger">Reject Purchase</button>
                <a href="{% url 'pharmacy:purchase_detail' purchase.id %}" class="btn btn-secondary">Cancel</a>
            </form>
        </div>
    </div>
</div>
{% endblock %}