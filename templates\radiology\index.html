{% extends 'base.html' %}
{% load radiology_tags %}

{% block title %}Radiology Dashboard - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Radiology Dashboard</h1>
        <a href="{% if patients and patients|length > 0 %}{% url 'radiology:order' patients.0.id %}{% else %}#{% endif %}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> New Radiology Order
        </a>
    </div>

    <!-- Patient List with Results Link -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <strong>Patients (Quick Access to Results)</strong>
                </div>
                <div class="card-body p-0">
                    <table class="table table-sm table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Patient Name</th>
                                <th>Patient Number</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for patient in patients %}
                            <tr>
                                <td>{{ patient.get_full_name }}</td>
                                <td>{{ patient.patient_number }}</td>
                                <td>
                                    <a href="{{ patient.results_url }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-file-medical-alt"></i> View Results
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr><td colspan="3">No patients found.</td></tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Pending Orders Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scheduled Orders Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Scheduled Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ scheduled_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Completed Orders Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Completed Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ completed_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Orders Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-4 offset-md-8">
            <form method="get" class="d-flex align-items-center" action="">
                <input type="text" name="patient_id" class="form-control me-2" placeholder="Filter by Patient ID" value="{{ request.GET.patient_id|default:'' }}">
                <button type="submit" class="btn btn-outline-primary">Filter</button>
            </form>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Radiology Orders</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                            aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">Filter By:</div>
                            <a class="dropdown-item" href="?status=pending">Pending</a>
                            <a class="dropdown-item" href="?status=scheduled">Scheduled</a>
                            <a class="dropdown-item" href="?status=completed">Completed</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="?">All Orders</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Patient</th>
                                    <th>Test</th>
                                    <th>Order Date</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                    {% if not request.GET.patient_id or order.patient.patient_id|stringformat:'s' == request.GET.patient_id|stringformat:'s' %}
                                    <tr>
                                        <td>RAD-{{ order.id|stringformat:'03d' }}</td>
                                        <td>{{ order.patient.get_full_name }}<br><small class="text-muted">ID: {{ order.patient.patient_id }}</small></td>
                                        <td>{{ order.test.name }}</td>
                                        <td>{{ order.order_date|date:'Y-m-d H:i' }}</td>
                                        <td>{{ order.status|radiology_status_badge }}</td>
                                        <td>{{ order.priority|priority_badge }}</td>
                                        <td>
                                            <a href="{% url 'radiology:order_detail' order.id %}" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endif %}
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted">No recent radiology orders found.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable();
    });
</script>
{% endblock %}
