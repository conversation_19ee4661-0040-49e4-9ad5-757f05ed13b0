{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Submit Review - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Submit Review</h1>
        <a href="{% url 'doctors:doctor_detail' doctor.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-1"></i> Back to Doctor Profile
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Review for {{ doctor.get_full_name }}</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        {% if doctor.user.profile.profile_picture %}
                            <img src="{{ doctor.user.profile.profile_picture.url }}" alt="{{ doctor.get_full_name }}" class="img-profile rounded-circle" style="width: 100px; height: 100px; object-fit: cover;">
                        {% else %}
                            <img src="/static/img/undraw_profile.svg" alt="{{ doctor.get_full_name }}" class="img-profile rounded-circle" style="width: 100px; height: 100px; object-fit: cover;">
                        {% endif %}
                        <h5 class="mt-3 mb-0">{{ doctor.get_full_name }}</h5>
                        <p class="text-muted">{{ doctor.specialization.name }}</p>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <label for="{{ form.rating.id_for_label }}" class="form-label">Rating *</label>
                            {{ form.rating|add_class:"form-select" }}
                            {% if form.rating.errors %}
                                <div class="text-danger">{{ form.rating.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-4">
                            <label for="{{ form.review_text.id_for_label }}" class="form-label">Your Review *</label>
                            {{ form.review_text|add_class:"form-control" }}
                            {% if form.review_text.errors %}
                                <div class="text-danger">{{ form.review_text.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-star mr-1"></i> Submit Review
                            </button>
                            <a href="{% url 'doctors:doctor_detail' doctor.id %}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
