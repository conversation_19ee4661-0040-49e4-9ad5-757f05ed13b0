{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.patient.id_for_label }}" class="form-label">Patient</label>
                            {{ form.patient|add_class:"form-select select2" }}
                            {% if form.patient.errors %}
                                <div class="text-danger">
                                    {{ form.patient.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.doctor.id_for_label }}" class="form-label">Doctor</label>
                            {{ form.doctor|add_class:"form-select select2" }}
                            {% if form.doctor.errors %}
                                <div class="text-danger">
                                    {{ form.doctor.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.appointment_date.id_for_label }}" class="form-label">Appointment Date</label>
                            {{ form.appointment_date|add_class:"form-control" }}
                            {% if form.appointment_date.errors %}
                                <div class="text-danger">
                                    {{ form.appointment_date.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.appointment_time.id_for_label }}" class="form-label">Start Time</label>
                            {{ form.appointment_time|add_class:"form-control" }}
                            {% if form.appointment_time.errors %}
                                <div class="text-danger">
                                    {{ form.appointment_time.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.end_time.id_for_label }}" class="form-label">End Time (Optional)</label>
                            {{ form.end_time|add_class:"form-control" }}
                            {% if form.end_time.errors %}
                                <div class="text-danger">
                                    {{ form.end_time.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                            {{ form.status|add_class:"form-select" }}
                            {% if form.status.errors %}
                                <div class="text-danger">
                                    {{ form.status.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.priority.id_for_label }}" class="form-label">Priority</label>
                            {{ form.priority|add_class:"form-select" }}
                            {% if form.priority.errors %}
                                <div class="text-danger">
                                    {{ form.priority.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.reason.id_for_label }}" class="form-label">Reason for Appointment</label>
                            {{ form.reason|add_class:"form-control" }}
                            {% if form.reason.errors %}
                                <div class="text-danger">
                                    {{ form.reason.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">Additional Notes (Optional)</label>
                            {{ form.notes|add_class:"form-control" }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {{ form.notes.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'appointments:list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Appointments
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Appointment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for patient and doctor dropdowns
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
        
        // Get doctor schedule when doctor is selected
        $('#{{ form.doctor.id_for_label }}').on('change', function() {
            var doctorId = $(this).val();
            var appointmentDate = $('#{{ form.appointment_date.id_for_label }}').val();
            
            if (doctorId && appointmentDate) {
                // You can add AJAX call here to get doctor's schedule for the selected date
                // and update available time slots
            }
        });
        
        // Update available time slots when date is changed
        $('#{{ form.appointment_date.id_for_label }}').on('change', function() {
            var appointmentDate = $(this).val();
            var doctorId = $('#{{ form.doctor.id_for_label }}').val();
            
            if (doctorId && appointmentDate) {
                // You can add AJAX call here to get doctor's schedule for the selected date
                // and update available time slots
            }
        });
    });
</script>
{% endblock %}
