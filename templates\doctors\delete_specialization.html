{% extends 'base.html' %}

{% block title %}Delete Specialization - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Delete Specialization</h1>
        <a href="{% url 'doctors:manage_specializations' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-1"></i> Back to Specializations
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Confirm Deletion</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h5 class="alert-heading">Warning!</h5>
                <p>Are you sure you want to delete the specialization "{{ specialization.name }}"?</p>
                
                {% if specialization.doctors.exists %}
                    <hr>
                    <p class="mb-0">
                        <strong>This specialization is currently assigned to {{ specialization.doctors.count }} doctor(s).</strong> 
                        Deleting it will remove the specialization from these doctors.
                    </p>
                {% endif %}
            </div>
            
            <form method="post">
                {% csrf_token %}
                <div class="text-center">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash mr-1"></i> Delete Specialization
                    </button>
                    <a href="{% url 'doctors:manage_specializations' %}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
