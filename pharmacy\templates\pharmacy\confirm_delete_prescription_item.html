{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Confirm Deletion</h6>
        </div>
        <div class="card-body">
            <p>Are you sure you want to delete the prescription item: <strong>{{ prescription_item.medication.name }} ({{ prescription_item.quantity }} units)</strong> from Prescription #{{ prescription.id }}?</p>
            <form method="post">
                {% csrf_token %}
                <button type="submit" class="btn btn-danger">Delete</button>
                <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-secondary">Cancel</a>
            </form>
        </div>
    </div>
</div>
{% endblock %}