{% extends 'base.html' %}
{% load core_form_tags %}
{% load radiology_tags %}

{% block title %}New Radiology Order - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">New Radiology Order</h1>
        <a href="{% url 'radiology:index' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Dashboard
        </a>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Details</h6>
                </div>
                <div class="card-body">
                    <form method="post" id="radiologyOrderForm">
                        {% csrf_token %}
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    {% for field in form %}
                                        {% for error in field.errors %}
                                            <li><strong>{{ field.label }}:</strong> {{ error }}</li>
                                        {% endfor %}
                                    {% endfor %}
                                    {% for error in form.non_field_errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                        <div class="alert alert-info mb-4" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            Please fill all required fields. Fields marked with <span class="text-danger">*</span> are mandatory.
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="id_patient" class="form-label">Patient <span class="text-danger">*</span></label>
                                {{ form.patient|add_class:"form-select select2" }}
                                <div class="form-text">Select the patient for this order. This field is locked if context is set.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="id_test" class="form-label">Radiology Test <span class="text-danger">*</span></label>
                                {{ form.test|add_class:"form-select select2" }}
                                <div class="form-text">Choose the required radiology test.</div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="id_priority" class="form-label">Priority <span class="text-danger">*</span></label>
                                {{ form.priority|add_class:"form-select" }}
                                <div class="form-text">Set the urgency of this order.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="id_scheduled_date" class="form-label">Scheduled Date & Time</label>
                                {{ form.scheduled_date|add_class:"form-control" }}
                                <div class="form-text">(Optional) Schedule the test for a specific date and time.</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="id_clinical_information" class="form-label">Clinical Information <span class="text-danger">*</span></label>
                            {{ form.clinical_information|add_class:"form-control" }}
                            <div class="form-text">Provide relevant clinical details for the radiology test.</div>
                        </div>
                        <div class="mb-3">
                            <label for="id_notes" class="form-label">Additional Notes</label>
                            {{ form.notes|add_class:"form-control" }}
                        </div>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-secondary me-md-2" onclick="window.location.href='{% url 'radiology:index' %}'">
                                <i class="fas fa-times me-1"></i> Cancel
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Create Order
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
