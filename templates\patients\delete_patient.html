{% extends 'base.html' %}
{% load static %}

{% block title %}Deactivate Patient{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header bg-warning text-white py-3">
                    <h6 class="m-0 font-weight-bold">Deactivate Patient</h6>
                </div>
                <div class="card-body">
                    <p class="lead">Are you sure you want to deactivate the patient "{{ patient.first_name }} {{ patient.last_name }}"?</p>

                    <div class="alert alert-info">
                        <h6 class="alert-heading">ℹ️ Important Information</h6>
                        <p class="mb-0">
                            This action will <strong>deactivate</strong> the patient, not permanently delete them.
                            The patient's data will be preserved and can be reactivated later if needed.
                        </p>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">Patient Details</h5>
                        <ul class="mb-0">
                            <li><strong>Name:</strong> {{ patient.first_name }} {{ patient.last_name }}</li>
                            <li><strong>Date of Birth:</strong> {{ patient.date_of_birth|date:"F j, Y" }}</li>
                            <li><strong>Gender:</strong> {{ patient.get_gender_display }}</li>
                            <li><strong>Contact:</strong> {{ patient.contact_number }}</li>
                        </ul>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'patients:detail' patient.pk %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-user-times me-1"></i> Deactivate Patient
                            </button>
                        </div>
                    </form>

                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>Note:</strong> To reactivate this patient later, use the management command:
                            <code>python manage.py activate_patients --patient-id {{ patient.patient_id }}</code>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}