{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Medications Below Reorder Level</h6>
        </div>
        <div class="card-body">
            {% if low_stock_items %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Medication</th>
                            <th>Dispensary</th>
                            <th>Current Stock</th>
                            <th>Reorder Level</th>
                            <th>Difference</th>
                            <th>Last Restock</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in low_stock_items %}
                        <tr>
                            <td>{{ item.medication.name }} ({{ item.medication.strength }})</td>
                            <td>{{ item.dispensary.name }}</td>
                            <td>{{ item.stock_quantity }}</td>
                            <td>{{ item.reorder_level }}</td>
                            <td class="text-danger">{{ item.stock_quantity|add:"-"|add:item.reorder_level }}</td>
                            <td>{{ item.last_restock_date|date:"Y-m-d H:i"|default:"N/A" }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p>No medications are currently below their reorder level.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}