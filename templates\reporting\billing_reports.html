{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ title }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Bills</h6>
        </div>
        <div class="card-body">
            <form method="get">
                <div class="row">
                    <div class="col-md-3">{{ form.start_date.label_tag }} {{ form.start_date }}</div>
                    <div class="col-md-3">{{ form.end_date.label_tag }} {{ form.end_date }}</div>
                    <div class="col-md-3">{{ form.patient.label_tag }} {{ form.patient }}</div>
                    <div class="col-md-3">{{ form.payment_status.label_tag }} {{ form.payment_status }}</div>
                </div>
                <button type="submit" class="btn btn-primary mt-3">Filter</button>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Bill List</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Patient</th>
                            <th>Total Amount</th>
                            <th>Date</th>
                            <th>Payment Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for bill in page_obj %}
                        <tr>
                            <td>{{ bill.id }}</td>
                            <td>{{ bill.patient.get_full_name }}</td>
                            <td>{{ bill.total_amount }}</td>
                            <td>{{ bill.created_at }}</td>
                            <td>{{ bill.get_payment_status_display }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">No bills found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% include 'pagination.html' %}
        </div>
    </div>
</div>
{% endblock %}