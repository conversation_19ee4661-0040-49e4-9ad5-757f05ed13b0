{"summary": {"total_tests": 1001, "passed": 949, "failed": 12, "warnings": 40, "success_rate": 94.*************, "forms_discovered": 126}, "form_classes": {"auth": ["UserCreationForm", "AuthenticationForm", "PasswordResetForm", "UserChangeForm"], "accounts": ["CustomLoginForm", "CustomUserCreationForm", "UserRegistrationForm", "UserProfileForm", "StaffCreationForm", "DepartmentForm", "PhoneNumberPasswordResetForm", "RoleForm", "UserRoleAssignmentForm", "BulkUserActionForm", "PermissionFilterForm", "AdvancedUserSearchForm"], "patients": ["PatientForm", "MedicalHistoryForm", "VitalsForm", "PatientSearchForm", "AddFundsForm", "WalletWithdrawalForm", "WalletTransferForm", "WalletRefundForm", "WalletAdjustmentForm", "WalletTransactionSearchForm", "NHIARegistrationForm", "NHIAIndependentPatientForm", "RetainershipRegistrationForm", "RetainershipIndependentPatientForm"], "doctors": ["SpecializationForm", "DoctorUserCreationForm", "DoctorAdminForm", "<PERSON><PERSON><PERSON>", "DoctorAvailabilityForm", "DoctorEducationForm", "DoctorEx<PERSON>ienceForm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Doctor<PERSON>earchForm"], "appointments": ["Doctor<PERSON><PERSON><PERSON><PERSON><PERSON>", "AppointmentForm", "AppointmentFollowUpForm", "Doctor<PERSON><PERSON>uleForm", "AppointmentSearchForm"], "pharmacy": ["MedicationCategoryForm", "MedicationForm", "SupplierForm", "PurchaseForm", "PurchaseItemForm", "PrescriptionForm", "PrescriptionItemForm", "DispenseItemForm", "MedicationSearchForm", "PrescriptionSearchForm", "DispensedItemsSearchForm", "DispensaryForm", "PrescriptionPaymentForm", "MedicationInventoryForm"], "laboratory": ["TestCategoryForm", "TestForm", "TestParameterForm", "TestRequestForm", "TestResultForm", "TestResultParameterForm", "TestSearchForm", "TestRequestSearchForm", "TestResultSearchForm"], "billing": ["InvoiceForm", "InvoiceItemForm", "PaymentForm", "AdmissionPaymentForm", "ServiceForm", "InvoiceSearchForm"], "inpatient": ["WardForm", "BedForm", "AdmissionForm", "DischargeForm", "DailyRoundForm", "NursingNoteForm", "AdmissionSearchForm", "ClinicalRecordForm", "PatientTransferForm"], "hr": ["DesignationForm", "ShiftForm", "StaffScheduleForm", "LeaveForm", "LeaveApprovalForm", "AttendanceForm", "PayrollForm", "StaffSearchForm", "LeaveSearchForm", "AttendanceSearchForm", "PayrollSearchForm"], "consultations": ["ConsultationForm", "ConsultationNoteForm", "ReferralForm", "VitalsSelectionForm", "ConsultingRoomForm", "WaitingListForm"], "radiology": ["RadiologyOrderForm", "RadiologyResultForm"], "theatre": ["OperationTheatreForm", "SurgeryTypeForm", "SurgeryForm", "SurgicalTeamForm", "SurgicalEquipmentForm", "EquipmentUsageForm", "SurgeryScheduleForm", "PostOperativeNoteForm", "PreOperativeChecklistForm", "SurgeryFilterForm"], "reporting": ["PatientReportForm", "AppointmentReportForm", "BillingReportForm", "PharmacySalesReportForm", "LaboratoryReportForm", "RadiologyReportForm", "InpatientReportForm", "HRReportForm", "FinancialReportForm", "ReportForm", "ReportExecutionForm", "DashboardForm", "DashboardWidgetForm", "ReportSearchForm", "DashboardSearchForm"]}, "results": [{"test": "UserCreationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.936313"}, {"test": "AuthenticationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.937109"}, {"test": "PasswordResetForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.937853"}, {"test": "UserChangeForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.939310"}, {"test": "CustomLoginForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.940292"}, {"test": "CustomUserCreationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.941361"}, {"test": "UserRegistrationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.942825"}, {"test": "UserProfileForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.944558"}, {"test": "StaffCreationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.946704"}, {"test": "DepartmentForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.948023"}, {"test": "PhoneNumberPasswordResetForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.952556"}, {"test": "RoleForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.954848"}, {"test": "UserRoleAssignmentForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.955990"}, {"test": "BulkUserActionForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.956990"}, {"test": "PermissionFilterForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.958406"}, {"test": "AdvancedUserSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.960324"}, {"test": "PatientForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.965190"}, {"test": "MedicalHistoryForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.967035"}, {"test": "VitalsForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.968005"}, {"test": "PatientSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.968959"}, {"test": "AddFundsForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.969974"}, {"test": "WalletWithdrawalForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.971288"}, {"test": "WalletTransferForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.972946"}, {"test": "WalletRefundForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.974032"}, {"test": "WalletAdjustmentForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.975029"}, {"test": "WalletTransactionSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.976028"}, {"test": "NHIARegistrationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.977226"}, {"test": "NHIAIndependentPatientForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.981103"}, {"test": "RetainershipRegistrationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.983305"}, {"test": "RetainershipIndependentPatientForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.984755"}, {"test": "SpecializationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.986004"}, {"test": "DoctorUserCreationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.987639"}, {"test": "DoctorAdminForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.988922"}, {"test": "<PERSON><PERSON><PERSON>", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.991020"}, {"test": "DoctorAvailabilityForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:50.992381"}, {"test": "DoctorEducationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.000971"}, {"test": "DoctorEx<PERSON>ienceForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.004050"}, {"test": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.024917"}, {"test": "Doctor<PERSON>earchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.031726"}, {"test": "Doctor<PERSON><PERSON><PERSON><PERSON><PERSON>", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.036587"}, {"test": "AppointmentForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.039937"}, {"test": "AppointmentFollowUpForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.041942"}, {"test": "Doctor<PERSON><PERSON>uleForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.051541"}, {"test": "AppointmentSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.053700"}, {"test": "MedicationCategoryForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.059044"}, {"test": "MedicationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.064957"}, {"test": "SupplierForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.067085"}, {"test": "PurchaseForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.071450"}, {"test": "PurchaseItemForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.072659"}, {"test": "PrescriptionForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.074685"}, {"test": "PrescriptionItemForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.076294"}, {"test": "DispenseItemForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.085275"}, {"test": "MedicationSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.087065"}, {"test": "PrescriptionSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.089480"}, {"test": "DispensedItemsSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.091588"}, {"test": "DispensaryForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.100899"}, {"test": "PrescriptionPaymentForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.102116"}, {"test": "MedicationInventoryForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.103964"}, {"test": "TestCategoryForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.105352"}, {"test": "TestForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.106567"}, {"test": "TestParameterForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.107501"}, {"test": "TestRequestForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.109907"}, {"test": "TestResultForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.111686"}, {"test": "TestResultParameterForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.115284"}, {"test": "TestSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.117249"}, {"test": "TestRequestSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.118204"}, {"test": "TestResultSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.119185"}, {"test": "InvoiceForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.120223"}, {"test": "InvoiceItemForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.121355"}, {"test": "PaymentForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.122738"}, {"test": "AdmissionPaymentForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.123727"}, {"test": "ServiceForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.124698"}, {"test": "InvoiceSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.125610"}, {"test": "WardForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.127068"}, {"test": "BedForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.128035"}, {"test": "AdmissionForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.131810"}, {"test": "DischargeForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.133443"}, {"test": "DailyRoundForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.134836"}, {"test": "NursingNoteForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.136985"}, {"test": "AdmissionSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.138169"}, {"test": "ClinicalRecordForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.140237"}, {"test": "PatientTransferForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.141393"}, {"test": "DesignationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.142588"}, {"test": "ShiftForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.143904"}, {"test": "StaffScheduleForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.147221"}, {"test": "LeaveForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.150676"}, {"test": "LeaveApprovalForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.151765"}, {"test": "AttendanceForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.153152"}, {"test": "PayrollForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.154978"}, {"test": "StaffSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.155931"}, {"test": "LeaveSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.156823"}, {"test": "AttendanceSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.157826"}, {"test": "PayrollSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.158751"}, {"test": "ConsultationForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.160585"}, {"test": "ConsultationNoteForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.161436"}, {"test": "ReferralForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.164745"}, {"test": "VitalsSelectionForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.166218"}, {"test": "ConsultingRoomForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.168230"}, {"test": "WaitingListForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.169520"}, {"test": "RadiologyOrderForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.171356"}, {"test": "RadiologyResultForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.172286"}, {"test": "OperationTheatreForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.173273"}, {"test": "SurgeryTypeForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.174247"}, {"test": "SurgeryForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.178038"}, {"test": "SurgicalTeamForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.182506"}, {"test": "SurgicalEquipmentForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.183655"}, {"test": "EquipmentUsageForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.184800"}, {"test": "SurgeryScheduleForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.185737"}, {"test": "PostOperativeNoteForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.186561"}, {"test": "PreOperativeChecklistForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.187754"}, {"test": "SurgeryFilterForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.189168"}, {"test": "PatientReportForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.190051"}, {"test": "AppointmentReportForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.190894"}, {"test": "BillingReportForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.191780"}, {"test": "PharmacySalesReportForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.193051"}, {"test": "LaboratoryReportForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.194061"}, {"test": "RadiologyReportForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.194907"}, {"test": "InpatientReportForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.197824"}, {"test": "HRReportForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.199333"}, {"test": "FinancialReportForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.200173"}, {"test": "ReportForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.201232"}, {"test": "ReportExecutionForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.202374"}, {"test": "DashboardForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.203332"}, {"test": "DashboardWidgetForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.204543"}, {"test": "ReportSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.205613"}, {"test": "DashboardSearchForm", "test_type": "FORM_INSTANTIATION", "status": "PASS", "message": "Form instantiated successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.209160"}, {"test": "UserCreationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "FAIL", "message": "Form validation error", "error": "Manager isn't available; 'auth.User' has been swapped for 'accounts.CustomUser'", "details": null, "timestamp": "2025-08-02T08:54:51.211509"}, {"test": "UserCreationForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "FAIL", "message": "Form validation error", "error": "Manager isn't available; 'auth.User' has been swapped for 'accounts.CustomUser'", "details": null, "timestamp": "2025-08-02T08:54:51.215785"}, {"test": "UserCreationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "FAIL", "message": "Form validation error", "error": "Manager isn't available; 'auth.User' has been swapped for 'accounts.CustomUser'", "details": null, "timestamp": "2025-08-02T08:54:51.217216"}, {"test": "UserCreationForm_future_date", "test_type": "FORM_VALIDATION", "status": "FAIL", "message": "Form validation error", "error": "Manager isn't available; 'auth.User' has been swapped for 'accounts.CustomUser'", "details": null, "timestamp": "2025-08-02T08:54:51.218890"}, {"test": "UserCreationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "FAIL", "message": "Form validation error", "error": "Manager isn't available; 'auth.User' has been swapped for 'accounts.CustomUser'", "details": null, "timestamp": "2025-08-02T08:54:51.221744"}, {"test": "UserCreationForm_long_text", "test_type": "FORM_VALIDATION", "status": "FAIL", "message": "Form validation error", "error": "Manager isn't available; 'auth.User' has been swapped for 'accounts.CustomUser'", "details": null, "timestamp": "2025-08-02T08:54:51.223636"}, {"test": "AuthenticationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.225458"}, {"test": "AuthenticationForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.226860"}, {"test": "AuthenticationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.229159"}, {"test": "AuthenticationForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.232495"}, {"test": "AuthenticationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.234087"}, {"test": "AuthenticationForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.234968"}, {"test": "PasswordResetForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.235692"}, {"test": "PasswordResetForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.237109"}, {"test": "PasswordResetForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.238369"}, {"test": "PasswordResetForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.239128"}, {"test": "PasswordResetForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.239994"}, {"test": "PasswordResetForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.240781"}, {"test": "UserChangeForm_empty_data", "test_type": "FORM_VALIDATION", "status": "FAIL", "message": "Form validation error", "error": "Manager isn't available; 'auth.User' has been swapped for 'accounts.CustomUser'", "details": null, "timestamp": "2025-08-02T08:54:51.242723"}, {"test": "UserChangeForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "FAIL", "message": "Form validation error", "error": "Manager isn't available; 'auth.User' has been swapped for 'accounts.CustomUser'", "details": null, "timestamp": "2025-08-02T08:54:51.244521"}, {"test": "UserChangeForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "FAIL", "message": "Form validation error", "error": "Manager isn't available; 'auth.User' has been swapped for 'accounts.CustomUser'", "details": null, "timestamp": "2025-08-02T08:54:51.249968"}, {"test": "UserChangeForm_future_date", "test_type": "FORM_VALIDATION", "status": "FAIL", "message": "Form validation error", "error": "Manager isn't available; 'auth.User' has been swapped for 'accounts.CustomUser'", "details": null, "timestamp": "2025-08-02T08:54:51.251773"}, {"test": "UserChangeForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "FAIL", "message": "Form validation error", "error": "Manager isn't available; 'auth.User' has been swapped for 'accounts.CustomUser'", "details": null, "timestamp": "2025-08-02T08:54:51.253928"}, {"test": "UserChangeForm_long_text", "test_type": "FORM_VALIDATION", "status": "FAIL", "message": "Form validation error", "error": "Manager isn't available; 'auth.User' has been swapped for 'accounts.CustomUser'", "details": null, "timestamp": "2025-08-02T08:54:51.256102"}, {"test": "CustomLoginForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.257404"}, {"test": "CustomLoginForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.258310"}, {"test": "CustomLoginForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.259309"}, {"test": "CustomLoginForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.260123"}, {"test": "CustomLoginForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.260844"}, {"test": "CustomLoginForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.261583"}, {"test": "CustomUserCreationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.266288"}, {"test": "CustomUserCreationForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.267597"}, {"test": "CustomUserCreationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.269673"}, {"test": "CustomUserCreationForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.271295"}, {"test": "CustomUserCreationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.272633"}, {"test": "CustomUserCreationForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.273832"}, {"test": "UserRegistrationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.275145"}, {"test": "UserRegistrationForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.277622"}, {"test": "UserRegistrationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.282440"}, {"test": "UserRegistrationForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.284318"}, {"test": "UserRegistrationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.285574"}, {"test": "UserRegistrationForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.286867"}, {"test": "UserProfileForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.288164"}, {"test": "UserProfileForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.290287"}, {"test": "UserProfileForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.291657"}, {"test": "UserProfileForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.293159"}, {"test": "UserProfileForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.294736"}, {"test": "UserProfileForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.298464"}, {"test": "StaffCreationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.300711"}, {"test": "StaffCreationForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.302525"}, {"test": "StaffCreationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.305137"}, {"test": "StaffCreationForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.306817"}, {"test": "StaffCreationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.308389"}, {"test": "StaffCreationForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.309794"}, {"test": "DepartmentForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.311305"}, {"test": "DepartmentForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.314292"}, {"test": "DepartmentForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.315919"}, {"test": "DepartmentForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.316987"}, {"test": "DepartmentForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.318375"}, {"test": "DepartmentForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.319559"}, {"test": "PhoneNumberPasswordResetForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.320589"}, {"test": "PhoneNumberPasswordResetForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.321821"}, {"test": "PhoneNumberPasswordResetForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.322624"}, {"test": "PhoneNumberPasswordResetForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.323440"}, {"test": "PhoneNumberPasswordResetForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.324228"}, {"test": "PhoneNumberPasswordResetForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.325232"}, {"test": "RoleForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.326793"}, {"test": "RoleForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.328162"}, {"test": "RoleForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.332252"}, {"test": "RoleForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.338308"}, {"test": "RoleForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.339730"}, {"test": "RoleForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.341091"}, {"test": "UserRoleAssignmentForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.342178"}, {"test": "UserRoleAssignmentForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.343224"}, {"test": "UserRoleAssignmentForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.344424"}, {"test": "UserRoleAssignmentForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.349412"}, {"test": "UserRoleAssignmentForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.352934"}, {"test": "UserRoleAssignmentForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.354126"}, {"test": "BulkUserActionForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.355146"}, {"test": "BulkUserActionForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.356326"}, {"test": "BulkUserActionForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.357494"}, {"test": "BulkUserActionForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.358898"}, {"test": "BulkUserActionForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.359846"}, {"test": "BulkUserActionForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.360765"}, {"test": "PermissionFilterForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.363910"}, {"test": "PermissionFilterForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.365672"}, {"test": "PermissionFilterForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.367246"}, {"test": "PermissionFilterForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.368494"}, {"test": "PermissionFilterForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.369642"}, {"test": "PermissionFilterForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.370753"}, {"test": "AdvancedUserSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.372053"}, {"test": "AdvancedUserSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.373175"}, {"test": "AdvancedUserSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.374343"}, {"test": "AdvancedUserSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.375639"}, {"test": "AdvancedUserSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.376964"}, {"test": "AdvancedUserSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.378365"}, {"test": "PatientForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.383483"}, {"test": "PatientForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.385412"}, {"test": "PatientForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.388227"}, {"test": "PatientForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.390315"}, {"test": "PatientForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.392810"}, {"test": "PatientForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.398474"}, {"test": "MedicalHistoryForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.399625"}, {"test": "MedicalHistoryForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.400619"}, {"test": "MedicalHistoryForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.401651"}, {"test": "MedicalHistoryForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.402614"}, {"test": "MedicalHistoryForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.404246"}, {"test": "MedicalHistoryForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.405365"}, {"test": "VitalsForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.406440"}, {"test": "VitalsForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.407425"}, {"test": "VitalsForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.408849"}, {"test": "VitalsForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.409875"}, {"test": "VitalsForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.411376"}, {"test": "VitalsForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.415149"}, {"test": "PatientSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.416131"}, {"test": "PatientSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.417170"}, {"test": "PatientSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.418135"}, {"test": "PatientSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.419340"}, {"test": "PatientSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.420576"}, {"test": "PatientSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.421597"}, {"test": "AddFundsForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.423082"}, {"test": "AddFundsForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.424374"}, {"test": "AddFundsForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.425417"}, {"test": "AddFundsForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.426769"}, {"test": "AddFundsForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.430584"}, {"test": "AddFundsForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.432550"}, {"test": "WalletWithdrawalForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.433713"}, {"test": "WalletWithdrawalForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.435426"}, {"test": "WalletWithdrawalForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.436784"}, {"test": "WalletWithdrawalForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.438181"}, {"test": "WalletWithdrawalForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.441350"}, {"test": "WalletWithdrawalForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.449337"}, {"test": "WalletTransferForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.451860"}, {"test": "WalletTransferForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.453648"}, {"test": "WalletTransferForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.456434"}, {"test": "WalletTransferForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.457848"}, {"test": "WalletTransferForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.458963"}, {"test": "WalletTransferForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.460460"}, {"test": "WalletRefundForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.463367"}, {"test": "WalletRefundForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.465272"}, {"test": "WalletRefundForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.466481"}, {"test": "WalletRefundForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.467686"}, {"test": "WalletRefundForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.468908"}, {"test": "WalletRefundForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.470318"}, {"test": "WalletAdjustmentForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.471395"}, {"test": "WalletAdjustmentForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.472771"}, {"test": "WalletAdjustmentForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.474079"}, {"test": "WalletAdjustmentForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.475635"}, {"test": "WalletAdjustmentForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.477025"}, {"test": "WalletAdjustmentForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.479028"}, {"test": "WalletTransactionSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.480496"}, {"test": "WalletTransactionSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.481873"}, {"test": "WalletTransactionSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.483265"}, {"test": "WalletTransactionSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.484673"}, {"test": "WalletTransactionSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.488924"}, {"test": "WalletTransactionSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.490446"}, {"test": "NHIARegistrationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.492145"}, {"test": "NHIARegistrationForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.493113"}, {"test": "NHIARegistrationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.494137"}, {"test": "NHIARegistrationForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.496470"}, {"test": "NHIARegistrationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.498826"}, {"test": "NHIARegistrationForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.500429"}, {"test": "NHIAIndependentPatientForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.502245"}, {"test": "NHIAIndependentPatientForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.504389"}, {"test": "NHIAIndependentPatientForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.506579"}, {"test": "NHIAIndependentPatientForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.508182"}, {"test": "NHIAIndependentPatientForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.509927"}, {"test": "NHIAIndependentPatientForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.513705"}, {"test": "RetainershipRegistrationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.515701"}, {"test": "RetainershipRegistrationForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.517467"}, {"test": "RetainershipRegistrationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.518437"}, {"test": "RetainershipRegistrationForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.519401"}, {"test": "RetainershipRegistrationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.520308"}, {"test": "RetainershipRegistrationForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.521194"}, {"test": "RetainershipIndependentPatientForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.525246"}, {"test": "RetainershipIndependentPatientForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.527231"}, {"test": "RetainershipIndependentPatientForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.532913"}, {"test": "RetainershipIndependentPatientForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.543746"}, {"test": "RetainershipIndependentPatientForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.651134"}, {"test": "RetainershipIndependentPatientForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.655402"}, {"test": "SpecializationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.657283"}, {"test": "SpecializationForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.658883"}, {"test": "SpecializationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.660416"}, {"test": "SpecializationForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.670883"}, {"test": "SpecializationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.689153"}, {"test": "SpecializationForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.690298"}, {"test": "DoctorUserCreationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.692308"}, {"test": "Doctor<PERSON>ser<PERSON><PERSON><PERSON>Form_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.694575"}, {"test": "Doctor<PERSON>ser<PERSON>reationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.700395"}, {"test": "DoctorUserCreationForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.702181"}, {"test": "DoctorUserCreationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.703656"}, {"test": "DoctorUserCreationForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.705128"}, {"test": "DoctorAdminForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.707752"}, {"test": "Doctor<PERSON>d<PERSON>F<PERSON>_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.709228"}, {"test": "Doctor<PERSON>dminForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.713980"}, {"test": "Doctor<PERSON>dminForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.716443"}, {"test": "DoctorAdminForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.717984"}, {"test": "DoctorAdminForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.720231"}, {"test": "DoctorForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.722590"}, {"test": "DoctorForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.724600"}, {"test": "DoctorF<PERSON>_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.726500"}, {"test": "Doctor<PERSON><PERSON>_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.729879"}, {"test": "DoctorForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.732944"}, {"test": "DoctorForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.734215"}, {"test": "DoctorAvailabilityForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.735889"}, {"test": "DoctorAvailabilityForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.737288"}, {"test": "DoctorAvailabilityForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.739197"}, {"test": "DoctorAvailabilityForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.740771"}, {"test": "DoctorAvailabilityForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.742024"}, {"test": "DoctorAvailabilityForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.744483"}, {"test": "DoctorEducationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.748398"}, {"test": "DoctorEducationForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.749724"}, {"test": "DoctorEducationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.750915"}, {"test": "DoctorEducationForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.752182"}, {"test": "DoctorEducationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.753386"}, {"test": "DoctorEducationForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.755679"}, {"test": "DoctorExperienceForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.756825"}, {"test": "DoctorExperienceForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.758164"}, {"test": "DoctorExperienceForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.759791"}, {"test": "DoctorExperienceForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.761082"}, {"test": "DoctorExperienceForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.765538"}, {"test": "DoctorExperienceForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.767635"}, {"test": "Doctor<PERSON><PERSON>iewForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.769152"}, {"test": "Doctor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.770391"}, {"test": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.771978"}, {"test": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.773403"}, {"test": "Doctor<PERSON><PERSON>iewForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.774442"}, {"test": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.775703"}, {"test": "DoctorSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.777473"}, {"test": "Doctor<PERSON><PERSON><PERSON><PERSON><PERSON>_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.780719"}, {"test": "Doctor<PERSON><PERSON><PERSON><PERSON><PERSON>_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.782515"}, {"test": "Doctor<PERSON><PERSON><PERSON><PERSON><PERSON>_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.783833"}, {"test": "DoctorSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.784857"}, {"test": "Doctor<PERSON><PERSON><PERSON><PERSON><PERSON>_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.786522"}, {"test": "DoctorLeaveForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.788080"}, {"test": "Doctor<PERSON><PERSON><PERSON><PERSON><PERSON>_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.790078"}, {"test": "Doctor<PERSON><PERSON><PERSON><PERSON><PERSON>_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.791675"}, {"test": "Doctor<PERSON><PERSON><PERSON><PERSON><PERSON>_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.793162"}, {"test": "DoctorLeaveForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.794294"}, {"test": "Doctor<PERSON><PERSON>ve<PERSON><PERSON>_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.798801"}, {"test": "AppointmentForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.800662"}, {"test": "AppointmentForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.802309"}, {"test": "AppointmentForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.805031"}, {"test": "AppointmentForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.807070"}, {"test": "AppointmentForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.809539"}, {"test": "AppointmentForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.815734"}, {"test": "AppointmentFollowUpForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.817008"}, {"test": "AppointmentFollowUpForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.818263"}, {"test": "AppointmentFollowUpForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.819963"}, {"test": "AppointmentFollowUpForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.821730"}, {"test": "AppointmentFollowUpForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.822789"}, {"test": "AppointmentFollowUpForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.823770"}, {"test": "DoctorScheduleForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.825130"}, {"test": "Doctor<PERSON><PERSON><PERSON><PERSON><PERSON>_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.827564"}, {"test": "Doctor<PERSON><PERSON>ule<PERSON><PERSON>_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.832847"}, {"test": "Doctor<PERSON><PERSON>uleForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.835091"}, {"test": "DoctorScheduleForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.840739"}, {"test": "Doctor<PERSON><PERSON>uleForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.843075"}, {"test": "AppointmentSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.846633"}, {"test": "AppointmentSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.851021"}, {"test": "AppointmentSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.852734"}, {"test": "AppointmentSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.856502"}, {"test": "AppointmentSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.860015"}, {"test": "AppointmentSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.866775"}, {"test": "MedicationCategoryForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.868783"}, {"test": "MedicationCategoryForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.870070"}, {"test": "MedicationCategoryForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.872962"}, {"test": "MedicationCategoryForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.876269"}, {"test": "MedicationCategoryForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.877812"}, {"test": "MedicationCategoryForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.888385"}, {"test": "MedicationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.892465"}, {"test": "MedicationForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.903416"}, {"test": "MedicationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.909354"}, {"test": "MedicationForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.915503"}, {"test": "MedicationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.918460"}, {"test": "MedicationForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.921258"}, {"test": "SupplierForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.923873"}, {"test": "SupplierForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.924961"}, {"test": "SupplierForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.925996"}, {"test": "SupplierForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.927583"}, {"test": "SupplierForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.929977"}, {"test": "SupplierForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.932442"}, {"test": "PurchaseForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.933645"}, {"test": "PurchaseForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.936034"}, {"test": "PurchaseForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.937669"}, {"test": "PurchaseForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.939104"}, {"test": "PurchaseForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.940266"}, {"test": "PurchaseForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.941537"}, {"test": "PurchaseItemForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.943668"}, {"test": "PurchaseItemForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.950495"}, {"test": "PurchaseItemForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.951917"}, {"test": "PurchaseItemForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.953270"}, {"test": "PurchaseItemForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.954524"}, {"test": "PurchaseItemForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.955711"}, {"test": "PrescriptionForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.957194"}, {"test": "PrescriptionForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.959016"}, {"test": "PrescriptionForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.960423"}, {"test": "PrescriptionForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.963326"}, {"test": "PrescriptionForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.966799"}, {"test": "PrescriptionForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.968513"}, {"test": "PrescriptionItemForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.969768"}, {"test": "PrescriptionItemForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.970968"}, {"test": "PrescriptionItemForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.972463"}, {"test": "PrescriptionItemForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.973908"}, {"test": "PrescriptionItemForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.975279"}, {"test": "PrescriptionItemForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.976362"}, {"test": "DispenseItemForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.981384"}, {"test": "DispenseItemForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.984278"}, {"test": "DispenseItemForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.986665"}, {"test": "DispenseItemForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.989645"}, {"test": "DispenseItemForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.992276"}, {"test": "DispenseItemForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.996313"}, {"test": "MedicationSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.998924"}, {"test": "MedicationSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:51.999857"}, {"test": "MedicationSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.000722"}, {"test": "MedicationSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.001664"}, {"test": "MedicationSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.002607"}, {"test": "MedicationSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.004280"}, {"test": "PrescriptionSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.005672"}, {"test": "PrescriptionSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.006686"}, {"test": "PrescriptionSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.008142"}, {"test": "PrescriptionSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.009319"}, {"test": "PrescriptionSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.010339"}, {"test": "PrescriptionSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.011296"}, {"test": "DispensedItemsSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.015789"}, {"test": "DispensedItemsSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.017297"}, {"test": "DispensedItemsSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.018478"}, {"test": "DispensedItemsSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.019874"}, {"test": "DispensedItemsSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.021531"}, {"test": "DispensedItemsSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.023142"}, {"test": "DispensaryForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.026653"}, {"test": "DispensaryForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.033807"}, {"test": "DispensaryForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.036578"}, {"test": "DispensaryForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.040284"}, {"test": "DispensaryForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.055961"}, {"test": "DispensaryForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.071028"}, {"test": "PrescriptionPaymentForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.072809"}, {"test": "PrescriptionPaymentForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.075118"}, {"test": "PrescriptionPaymentForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.077458"}, {"test": "PrescriptionPaymentForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.083977"}, {"test": "PrescriptionPaymentForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.085623"}, {"test": "PrescriptionPaymentForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.088328"}, {"test": "MedicationInventoryForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.090380"}, {"test": "MedicationInventoryForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.093138"}, {"test": "MedicationInventoryForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.097102"}, {"test": "MedicationInventoryForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.099247"}, {"test": "MedicationInventoryForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.100352"}, {"test": "MedicationInventoryForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.101361"}, {"test": "TestCategoryForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.102238"}, {"test": "TestCategoryForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.103088"}, {"test": "TestCategoryForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.104096"}, {"test": "TestCategoryForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.105532"}, {"test": "TestCategoryForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.106384"}, {"test": "TestCategoryForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.107310"}, {"test": "TestForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.108514"}, {"test": "TestForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.110905"}, {"test": "TestForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.114114"}, {"test": "TestForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.115730"}, {"test": "TestForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.117009"}, {"test": "TestForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.118262"}, {"test": "TestParameterForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.119124"}, {"test": "TestParameterForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.120218"}, {"test": "TestParameterForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.121624"}, {"test": "TestParameterForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.123128"}, {"test": "TestParameterForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.124066"}, {"test": "TestParameterForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.125072"}, {"test": "TestRequestForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.126799"}, {"test": "TestRequestForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.128184"}, {"test": "TestRequestForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.132742"}, {"test": "TestRequestForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.134125"}, {"test": "TestRequestForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.135513"}, {"test": "TestRequestForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.137377"}, {"test": "TestResultForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.138680"}, {"test": "TestResultForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.139946"}, {"test": "TestResultForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.141096"}, {"test": "TestResultForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.143066"}, {"test": "TestResultForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.144414"}, {"test": "TestResultForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.148084"}, {"test": "TestResultParameterForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.149505"}, {"test": "TestResultParameterForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.150561"}, {"test": "TestResultParameterForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.151839"}, {"test": "TestResultParameterForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.153107"}, {"test": "TestResultParameterForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.154659"}, {"test": "TestResultParameterForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.156119"}, {"test": "TestSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.157717"}, {"test": "TestSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.159121"}, {"test": "TestSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.160872"}, {"test": "TestSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.166544"}, {"test": "TestSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.168023"}, {"test": "TestSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.169590"}, {"test": "TestRequestSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.170590"}, {"test": "TestRequestSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.171653"}, {"test": "TestRequestSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.173275"}, {"test": "TestRequestSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.174336"}, {"test": "TestRequestSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.175297"}, {"test": "TestRequestSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.176355"}, {"test": "TestResultSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.177882"}, {"test": "TestResultSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.181724"}, {"test": "TestResultSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.183755"}, {"test": "TestResultSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.184900"}, {"test": "TestResultSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.185950"}, {"test": "TestResultSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.187143"}, {"test": "InvoiceForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.188431"}, {"test": "InvoiceForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.189633"}, {"test": "InvoiceForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.191091"}, {"test": "InvoiceForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.192332"}, {"test": "InvoiceForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.193547"}, {"test": "InvoiceForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.194595"}, {"test": "InvoiceItemForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.197999"}, {"test": "InvoiceItemForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.199618"}, {"test": "InvoiceItemForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.200729"}, {"test": "InvoiceItemForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.201900"}, {"test": "InvoiceItemForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.202956"}, {"test": "InvoiceItemForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.204457"}, {"test": "PaymentForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.205992"}, {"test": "PaymentForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.207129"}, {"test": "PaymentForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.208651"}, {"test": "PaymentForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.210136"}, {"test": "PaymentForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.211236"}, {"test": "PaymentForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.214467"}, {"test": "AdmissionPaymentForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.216326"}, {"test": "AdmissionPaymentForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.217387"}, {"test": "AdmissionPaymentForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.218616"}, {"test": "AdmissionPaymentForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.219741"}, {"test": "AdmissionPaymentForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.221063"}, {"test": "AdmissionPaymentForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.222420"}, {"test": "ServiceForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.223509"}, {"test": "ServiceForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.224399"}, {"test": "ServiceForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.226092"}, {"test": "ServiceForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.227439"}, {"test": "ServiceForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.232244"}, {"test": "ServiceForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.233459"}, {"test": "InvoiceSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.234542"}, {"test": "InvoiceSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.236282"}, {"test": "InvoiceSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.237169"}, {"test": "InvoiceSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.238481"}, {"test": "InvoiceSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.239788"}, {"test": "InvoiceSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.240641"}, {"test": "WardForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.242330"}, {"test": "WardForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.243610"}, {"test": "WardForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.244734"}, {"test": "WardForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.248913"}, {"test": "WardForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.250126"}, {"test": "WardForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.251205"}, {"test": "BedForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.252174"}, {"test": "BedForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.253129"}, {"test": "BedForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.254938"}, {"test": "BedForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.256064"}, {"test": "BedForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.257102"}, {"test": "BedForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.258090"}, {"test": "AdmissionForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.260208"}, {"test": "AdmissionForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.263724"}, {"test": "AdmissionForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.267461"}, {"test": "AdmissionForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.269159"}, {"test": "AdmissionForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.271174"}, {"test": "AdmissionForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.272946"}, {"test": "DischargeForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.273989"}, {"test": "DischargeForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.275164"}, {"test": "DischargeForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.276272"}, {"test": "DischargeForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.277268"}, {"test": "DischargeForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.279840"}, {"test": "DischargeForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.282656"}, {"test": "DailyRoundForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.283926"}, {"test": "DailyRoundForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.285181"}, {"test": "DailyRoundForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.286415"}, {"test": "DailyRoundForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.288022"}, {"test": "DailyRoundForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.289356"}, {"test": "DailyRoundForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.290594"}, {"test": "NursingNoteForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.293441"}, {"test": "NursingNoteForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.298108"}, {"test": "NursingNoteForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.300548"}, {"test": "NursingNoteForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.303162"}, {"test": "NursingNoteForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.305571"}, {"test": "NursingNoteForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.307708"}, {"test": "AdmissionSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.308799"}, {"test": "AdmissionSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.310513"}, {"test": "AdmissionSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.311558"}, {"test": "AdmissionSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.315257"}, {"test": "AdmissionSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.316567"}, {"test": "AdmissionSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.317548"}, {"test": "ClinicalRecordForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.318857"}, {"test": "ClinicalRecordForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.320181"}, {"test": "ClinicalRecordForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.321730"}, {"test": "ClinicalRecordForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.323056"}, {"test": "ClinicalRecordForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.324890"}, {"test": "ClinicalRecordForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.327007"}, {"test": "PatientTransferForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.328037"}, {"test": "PatientTransferForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.331663"}, {"test": "PatientTransferForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.332679"}, {"test": "PatientTransferForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.333660"}, {"test": "PatientTransferForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.334576"}, {"test": "PatientTransferForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.335427"}, {"test": "DesignationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.336310"}, {"test": "DesignationForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.337884"}, {"test": "DesignationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.338975"}, {"test": "DesignationForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.339903"}, {"test": "DesignationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.340829"}, {"test": "DesignationForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.341798"}, {"test": "ShiftForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.343220"}, {"test": "ShiftForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.344521"}, {"test": "ShiftForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.348607"}, {"test": "ShiftForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.349749"}, {"test": "ShiftForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.350760"}, {"test": "ShiftForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.351732"}, {"test": "StaffScheduleForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.354588"}, {"test": "StaffScheduleForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.356745"}, {"test": "StaffScheduleForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.358190"}, {"test": "StaffScheduleForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.359442"}, {"test": "StaffScheduleForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.360874"}, {"test": "StaffScheduleForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.364672"}, {"test": "LeaveForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.366243"}, {"test": "LeaveForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.367536"}, {"test": "LeaveForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.369352"}, {"test": "LeaveForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.370988"}, {"test": "LeaveForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.372726"}, {"test": "LeaveForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.373995"}, {"test": "LeaveApprovalForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.375084"}, {"test": "LeaveApprovalForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.376101"}, {"test": "LeaveApprovalForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.377129"}, {"test": "LeaveApprovalForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.378087"}, {"test": "LeaveApprovalForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.383201"}, {"test": "LeaveApprovalForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.384241"}, {"test": "AttendanceForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.385557"}, {"test": "AttendanceForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.387619"}, {"test": "AttendanceForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.389249"}, {"test": "AttendanceForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.390577"}, {"test": "AttendanceForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.392183"}, {"test": "AttendanceForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.393584"}, {"test": "PayrollForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.397544"}, {"test": "PayrollForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.399666"}, {"test": "PayrollForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.401046"}, {"test": "PayrollForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.402510"}, {"test": "PayrollForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.404398"}, {"test": "PayrollForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.406371"}, {"test": "StaffSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.407370"}, {"test": "StaffSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.408836"}, {"test": "StaffSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.409795"}, {"test": "StaffSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.410868"}, {"test": "StaffSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.413539"}, {"test": "StaffSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.415777"}, {"test": "LeaveSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.416730"}, {"test": "LeaveSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.417624"}, {"test": "LeaveSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.418576"}, {"test": "LeaveSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.419487"}, {"test": "LeaveSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.420843"}, {"test": "LeaveSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.422052"}, {"test": "AttendanceSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.422980"}, {"test": "AttendanceSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.423897"}, {"test": "AttendanceSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.424843"}, {"test": "AttendanceSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.426125"}, {"test": "AttendanceSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.427437"}, {"test": "AttendanceSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.429573"}, {"test": "PayrollSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.432115"}, {"test": "PayrollSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.433142"}, {"test": "PayrollSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.434193"}, {"test": "PayrollSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.435219"}, {"test": "PayrollSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.436868"}, {"test": "PayrollSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.437925"}, {"test": "ConsultationForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.439688"}, {"test": "ConsultationForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.441517"}, {"test": "ConsultationForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.443115"}, {"test": "ConsultationForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.444561"}, {"test": "ConsultationForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.449247"}, {"test": "ConsultationForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.450652"}, {"test": "ConsultationNoteForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.451468"}, {"test": "ConsultationNoteForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.452312"}, {"test": "ConsultationNoteForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.453464"}, {"test": "ConsultationNoteForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.454754"}, {"test": "ConsultationNoteForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.455645"}, {"test": "ConsultationNoteForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.456569"}, {"test": "ReferralForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.457704"}, {"test": "ReferralForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.459144"}, {"test": "ReferralForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.460378"}, {"test": "ReferralForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.476869"}, {"test": "ReferralForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.484620"}, {"test": "ReferralForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.486342"}, {"test": "VitalsSelectionForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.487452"}, {"test": "VitalsSelectionForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.488330"}, {"test": "VitalsSelectionForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.489168"}, {"test": "VitalsSelectionForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.491196"}, {"test": "VitalsSelectionForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.492337"}, {"test": "VitalsSelectionForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.493831"}, {"test": "ConsultingRoomForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.498838"}, {"test": "ConsultingRoomForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.500173"}, {"test": "ConsultingRoomForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.501560"}, {"test": "ConsultingRoomForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.502764"}, {"test": "ConsultingRoomForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.503887"}, {"test": "ConsultingRoomForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.505334"}, {"test": "WaitingListForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.506984"}, {"test": "WaitingListForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.508515"}, {"test": "WaitingListForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.509736"}, {"test": "WaitingListForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.510962"}, {"test": "WaitingListForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.515026"}, {"test": "WaitingListForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.516231"}, {"test": "RadiologyOrderForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.518158"}, {"test": "RadiologyOrderForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.519728"}, {"test": "RadiologyOrderForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.521425"}, {"test": "RadiologyOrderForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.522862"}, {"test": "RadiologyOrderForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.524228"}, {"test": "RadiologyOrderForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.526203"}, {"test": "RadiologyResultForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.527266"}, {"test": "RadiologyResultForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.529180"}, {"test": "RadiologyResultForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.532347"}, {"test": "RadiologyResultForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.533424"}, {"test": "RadiologyResultForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.534458"}, {"test": "RadiologyResultForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.535578"}, {"test": "OperationTheatreForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.536904"}, {"test": "OperationTheatreForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.538404"}, {"test": "OperationTheatreForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.539466"}, {"test": "OperationTheatreForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.540499"}, {"test": "OperationTheatreForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.542059"}, {"test": "OperationTheatreForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.543734"}, {"test": "SurgeryTypeForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.545898"}, {"test": "SurgeryTypeForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.549291"}, {"test": "SurgeryTypeForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.550483"}, {"test": "SurgeryTypeForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.551601"}, {"test": "SurgeryTypeForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.552798"}, {"test": "SurgeryTypeForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.553856"}, {"test": "SurgeryForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.556927"}, {"test": "SurgeryForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.560154"}, {"test": "SurgeryForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.565525"}, {"test": "SurgeryForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.568847"}, {"test": "SurgeryForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.571377"}, {"test": "SurgeryForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.574885"}, {"test": "SurgicalTeamForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.575847"}, {"test": "SurgicalTeamForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.576879"}, {"test": "SurgicalTeamForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.579727"}, {"test": "SurgicalTeamForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.582159"}, {"test": "SurgicalTeamForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.583203"}, {"test": "SurgicalTeamForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.584294"}, {"test": "SurgicalEquipmentForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.585450"}, {"test": "SurgicalEquipmentForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.586581"}, {"test": "SurgicalEquipmentForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.589062"}, {"test": "SurgicalEquipmentForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.590600"}, {"test": "SurgicalEquipmentForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.591735"}, {"test": "SurgicalEquipmentForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.593733"}, {"test": "EquipmentUsageForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.594763"}, {"test": "EquipmentUsageForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.599256"}, {"test": "EquipmentUsageForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.600257"}, {"test": "EquipmentUsageForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.601283"}, {"test": "EquipmentUsageForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.602245"}, {"test": "EquipmentUsageForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.603573"}, {"test": "SurgeryScheduleForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.605051"}, {"test": "SurgeryScheduleForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.606181"}, {"test": "SurgeryScheduleForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.607223"}, {"test": "SurgeryScheduleForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.608583"}, {"test": "SurgeryScheduleForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.609687"}, {"test": "SurgeryScheduleForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.610740"}, {"test": "PostOperativeNoteForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.613059"}, {"test": "PostOperativeNoteForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.615740"}, {"test": "PostOperativeNoteForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.616763"}, {"test": "PostOperativeNoteForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.617651"}, {"test": "PostOperativeNoteForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.618675"}, {"test": "PostOperativeNoteForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.619613"}, {"test": "PreOperativeChecklistForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.621157"}, {"test": "PreOperativeChecklistForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.622373"}, {"test": "PreOperativeChecklistForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.623527"}, {"test": "PreOperativeChecklistForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.624550"}, {"test": "PreOperativeChecklistForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.625788"}, {"test": "PreOperativeChecklistForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.627677"}, {"test": "SurgeryFilterForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.631005"}, {"test": "SurgeryFilterForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.632426"}, {"test": "SurgeryFilterForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.633905"}, {"test": "SurgeryFilterForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.635634"}, {"test": "SurgeryFilterForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.636995"}, {"test": "SurgeryFilterForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.638145"}, {"test": "PatientReportForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.639027"}, {"test": "PatientReportForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.639867"}, {"test": "PatientReportForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.640711"}, {"test": "PatientReportForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.641721"}, {"test": "PatientReportForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.642939"}, {"test": "PatientReportForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.643721"}, {"test": "AppointmentReportForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.644588"}, {"test": "AppointmentReportForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.648555"}, {"test": "AppointmentReportForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.649573"}, {"test": "AppointmentReportForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.650551"}, {"test": "AppointmentReportForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.651509"}, {"test": "AppointmentReportForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.652747"}, {"test": "BillingReportForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.653704"}, {"test": "BillingReportForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.654594"}, {"test": "BillingReportForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.655443"}, {"test": "BillingReportForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.656337"}, {"test": "BillingReportForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.657797"}, {"test": "BillingReportForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.659344"}, {"test": "PharmacySalesReportForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.660273"}, {"test": "PharmacySalesReportForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.661061"}, {"test": "PharmacySalesReportForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.664500"}, {"test": "PharmacySalesReportForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.665439"}, {"test": "PharmacySalesReportForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.666314"}, {"test": "PharmacySalesReportForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.667097"}, {"test": "LaboratoryReportForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.668447"}, {"test": "LaboratoryReportForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.669612"}, {"test": "LaboratoryReportForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.671924"}, {"test": "LaboratoryReportForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.673328"}, {"test": "LaboratoryReportForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.675298"}, {"test": "LaboratoryReportForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.676656"}, {"test": "RadiologyReportForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.677561"}, {"test": "RadiologyReportForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.681231"}, {"test": "RadiologyReportForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.682206"}, {"test": "RadiologyReportForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.683261"}, {"test": "RadiologyReportForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.684282"}, {"test": "RadiologyReportForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.685151"}, {"test": "InpatientReportForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.685944"}, {"test": "InpatientReportForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.686772"}, {"test": "InpatientReportForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.687575"}, {"test": "InpatientReportForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.688813"}, {"test": "InpatientReportForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.690038"}, {"test": "InpatientReportForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.690907"}, {"test": "HRReportForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.691780"}, {"test": "HRReportForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.692603"}, {"test": "HRReportForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.693642"}, {"test": "HRReportForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.694698"}, {"test": "HRReportForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.697839"}, {"test": "HRReportForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.698971"}, {"test": "FinancialReportForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.699859"}, {"test": "FinancialReportForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.700711"}, {"test": "FinancialReportForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.701796"}, {"test": "FinancialReportForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.702757"}, {"test": "FinancialReportForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.704069"}, {"test": "FinancialReportForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.704888"}, {"test": "ReportForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.705997"}, {"test": "ReportForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.707103"}, {"test": "ReportForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.708287"}, {"test": "ReportForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.709715"}, {"test": "ReportForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.710724"}, {"test": "ReportForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.714444"}, {"test": "ReportExecutionForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.715693"}, {"test": "ReportExecutionForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.716685"}, {"test": "ReportExecutionForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.717660"}, {"test": "ReportExecutionForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.718664"}, {"test": "ReportExecutionForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.719849"}, {"test": "ReportExecutionForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.721266"}, {"test": "DashboardForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.722560"}, {"test": "DashboardForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.723470"}, {"test": "DashboardForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.724385"}, {"test": "DashboardForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.725593"}, {"test": "DashboardForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.726488"}, {"test": "DashboardForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.727494"}, {"test": "DashboardWidgetForm_empty_data", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form correctly rejected empty data", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.730915"}, {"test": "DashboardWidgetForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.732788"}, {"test": "DashboardWidgetForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.734086"}, {"test": "DashboardWidgetForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.735662"}, {"test": "DashboardWidgetForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.736895"}, {"test": "DashboardWidgetForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: False)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.738624"}, {"test": "ReportSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.739649"}, {"test": "ReportSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.740561"}, {"test": "ReportSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.742872"}, {"test": "ReportSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.744474"}, {"test": "ReportSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.747978"}, {"test": "ReportSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.749136"}, {"test": "DashboardSearchForm_empty_data", "test_type": "FORM_VALIDATION", "status": "WARN", "message": "Form accepted empty data (may be valid)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.750546"}, {"test": "DashboardSearchForm_invalid_email", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.751415"}, {"test": "DashboardSearchForm_invalid_phone", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.752195"}, {"test": "DashboardSearchForm_future_date", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.752947"}, {"test": "DashboardSearchForm_negative_amount", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.753709"}, {"test": "DashboardSearchForm_long_text", "test_type": "FORM_VALIDATION", "status": "PASS", "message": "Form validation completed (valid: True)", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.755016"}, {"test": "PasswordResetForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for '<EMAIL>'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.758131"}, {"test": "PasswordResetForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for 'invalid-email'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.758736"}, {"test": "PasswordResetForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for ''", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.759855"}, {"test": "UserChangeForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for '<EMAIL>'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.761110"}, {"test": "UserChangeForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for 'invalid-email'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.763635"}, {"test": "UserChangeForm.email", "test_type": "FIELD_VALIDATION", "status": "WARN", "message": "Email validation unexpected for ''", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.764996"}, {"test": "CustomUserCreationForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for '<EMAIL>'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.766127"}, {"test": "CustomUserCreationForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for 'invalid-email'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.766741"}, {"test": "CustomUserCreationForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for ''", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.767446"}, {"test": "UserRegistrationForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for '<EMAIL>'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.768499"}, {"test": "UserRegistrationForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for 'invalid-email'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.769078"}, {"test": "UserRegistrationForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for ''", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.769635"}, {"test": "UserProfileForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for '<EMAIL>'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.771329"}, {"test": "UserProfileForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for 'invalid-email'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.771985"}, {"test": "UserProfileForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for ''", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.772566"}, {"test": "StaffCreationForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for '<EMAIL>'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.773632"}, {"test": "StaffCreationForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for 'invalid-email'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.774196"}, {"test": "StaffCreationForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for ''", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.774750"}, {"test": "PhoneNumberPasswordResetForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for '<EMAIL>'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.776402"}, {"test": "PhoneNumberPasswordResetForm.email", "test_type": "FIELD_VALIDATION", "status": "WARN", "message": "Email validation unexpected for 'invalid-email'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.776988"}, {"test": "PhoneNumberPasswordResetForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for ''", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.777615"}, {"test": "PatientForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for '<EMAIL>'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.783095"}, {"test": "PatientForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for 'invalid-email'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.783742"}, {"test": "PatientForm.email", "test_type": "FIELD_VALIDATION", "status": "WARN", "message": "Email validation unexpected for ''", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.784283"}, {"test": "NHIAIndependentPatientForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for '<EMAIL>'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.786709"}, {"test": "NHIAIndependentPatientForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for 'invalid-email'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.787335"}, {"test": "NHIAIndependentPatientForm.email", "test_type": "FIELD_VALIDATION", "status": "WARN", "message": "Email validation unexpected for ''", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.788484"}, {"test": "RetainershipIndependentPatientForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for '<EMAIL>'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.790020"}, {"test": "RetainershipIndependentPatientForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for 'invalid-email'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.790690"}, {"test": "RetainershipIndependentPatientForm.email", "test_type": "FIELD_VALIDATION", "status": "WARN", "message": "Email validation unexpected for ''", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.791527"}, {"test": "DoctorUserCreationForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for '<EMAIL>'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.792674"}, {"test": "DoctorUserCreationForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for 'invalid-email'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.793438"}, {"test": "DoctorUserCreationForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for ''", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.794611"}, {"test": "SupplierForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for '<EMAIL>'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.801434"}, {"test": "SupplierForm.email", "test_type": "FIELD_VALIDATION", "status": "PASS", "message": "Email validation correct for 'invalid-email'", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.802018"}, {"test": "SupplierForm.email", "test_type": "FIELD_VALIDATION", "status": "WARN", "message": "Email validation unexpected for ''", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.802553"}, {"test": "UserCreationForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.828029"}, {"test": "UserChangeForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.832264"}, {"test": "CustomUserCreationForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.833606"}, {"test": "UserRegistrationForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.834858"}, {"test": "UserProfileForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.835908"}, {"test": "StaffCreationForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.836925"}, {"test": "DepartmentForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.837819"}, {"test": "RoleForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.840183"}, {"test": "UserRoleAssignmentForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.841210"}, {"test": "PatientForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.842527"}, {"test": "MedicalHistoryForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.843607"}, {"test": "VitalsForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.844764"}, {"test": "NHIARegistrationForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.848566"}, {"test": "NHIAIndependentPatientForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.851958"}, {"test": "RetainershipRegistrationForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.853026"}, {"test": "RetainershipIndependentPatientForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.857311"}, {"test": "SpecializationForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.858285"}, {"test": "DoctorUserCreationForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.859755"}, {"test": "DoctorAdminForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.861206"}, {"test": "<PERSON><PERSON><PERSON>", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.865373"}, {"test": "DoctorAvailabilityForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.866444"}, {"test": "DoctorEducationForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.867408"}, {"test": "DoctorEx<PERSON>ienceForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.868383"}, {"test": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.869556"}, {"test": "Doctor<PERSON><PERSON><PERSON><PERSON><PERSON>", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.870929"}, {"test": "AppointmentForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.872700"}, {"test": "AppointmentFollowUpForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.873628"}, {"test": "Doctor<PERSON><PERSON>uleForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.874682"}, {"test": "MedicationCategoryForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.875908"}, {"test": "MedicationForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.877386"}, {"test": "SupplierForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.880782"}, {"test": "PurchaseForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.881994"}, {"test": "PurchaseItemForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.883165"}, {"test": "PrescriptionForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.884283"}, {"test": "PrescriptionItemForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.885642"}, {"test": "DispensaryForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.888028"}, {"test": "PrescriptionPaymentForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.889234"}, {"test": "MedicationInventoryForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.890499"}, {"test": "TestCategoryForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.891549"}, {"test": "TestForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.893519"}, {"test": "TestParameterForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.897767"}, {"test": "TestRequestForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.900753"}, {"test": "TestResultForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.902650"}, {"test": "TestResultParameterForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.903891"}, {"test": "InvoiceForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.906734"}, {"test": "InvoiceItemForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.908145"}, {"test": "PaymentForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.910024"}, {"test": "AdmissionPaymentForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.911467"}, {"test": "ServiceForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.916505"}, {"test": "WardForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.918044"}, {"test": "BedForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.919388"}, {"test": "AdmissionForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.921604"}, {"test": "DischargeForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.923922"}, {"test": "DailyRoundForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.925762"}, {"test": "NursingNoteForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.932472"}, {"test": "ClinicalRecordForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.934442"}, {"test": "DesignationForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.935692"}, {"test": "ShiftForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.936837"}, {"test": "StaffScheduleForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.939545"}, {"test": "LeaveForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.941219"}, {"test": "LeaveApprovalForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.942619"}, {"test": "AttendanceForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.944740"}, {"test": "PayrollForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.952032"}, {"test": "ConsultationForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.955971"}, {"test": "ConsultationNoteForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.957411"}, {"test": "ReferralForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.959055"}, {"test": "ConsultingRoomForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.961038"}, {"test": "WaitingListForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.966617"}, {"test": "RadiologyOrderForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.968696"}, {"test": "RadiologyResultForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.970530"}, {"test": "OperationTheatreForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.972507"}, {"test": "SurgeryTypeForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.973986"}, {"test": "SurgeryForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.981752"}, {"test": "SurgicalTeamForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.984328"}, {"test": "SurgicalEquipmentForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.985901"}, {"test": "EquipmentUsageForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.987406"}, {"test": "SurgeryScheduleForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.990161"}, {"test": "PostOperativeNoteForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.991469"}, {"test": "PreOperativeChecklistForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.992844"}, {"test": "ReportForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.994779"}, {"test": "ReportExecutionForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:52.998303"}, {"test": "DashboardForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:53.000145"}, {"test": "DashboardWidgetForm", "test_type": "FORM_SAVE", "status": "PASS", "message": "Form has save method available", "error": null, "details": null, "timestamp": "2025-08-02T08:54:53.001865"}]}