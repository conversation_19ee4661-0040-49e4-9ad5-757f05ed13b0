{% extends 'base.html' %}

{% block title %}
{{ title }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ title }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Financial Reports</h6>
        </div>
        <div class="card-body">
            <form method="get" class="form-inline">
                {{ form.as_p }}
                <button type="submit" class="btn btn-primary ml-2">Filter</button>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Financial Report</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Type</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in page_obj %}
                        <tr>
                            <td>{{ item.date }}</td>
                            <td>{{ item.description }}</td>
                            <td>{{ item.type }}</td>
                            <td>{{ item.amount }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% include 'partials/_pagination.html' %}
        </div>
    </div>
</div>
{% endblock %}