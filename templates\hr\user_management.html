{% extends 'base.html' %}
{% load core_form_tags %}

{% block title %}User Management - Hospital Management System{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-3">User Management</h1>
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary mb-3">
                <div class="card-body">
                    <h5 class="card-title">Total Users</h5>
                    <p class="card-text display-6">{{ total_staff }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-white bg-success mb-3">
                <div class="card-body">
                    <h5 class="card-title">Doctors</h5>
                    <p class="card-text display-6">{{ doctors_count }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-white bg-info mb-3">
                <div class="card-body">
                    <h5 class="card-title">Nurses</h5>
                    <p class="card-text display-6">{{ nurses_count }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-white bg-warning mb-3">
                <div class="card-body">
                    <h5 class="card-title">Admins</h5>
                    <p class="card-text display-6">{{ admin_count }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-secondary mb-3">
                <div class="card-body">
                    <h5 class="card-title">Other Roles</h5>
                    <p class="card-text display-6">{{ other_count }}</p>
                </div>
            </div>
        </div>
    </div>
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="form-inline row g-3">
                <div class="col-md-3">
                    {{ search_form.search.label_tag }}
                    {{ search_form.search|add_class:'form-control' }}
                </div>
                <div class="col-md-2">
                    {{ search_form.department.label_tag }}
                    {{ search_form.department|add_class:'form-select' }}
                </div>
                <div class="col-md-2">
                    {{ search_form.role.label_tag }}
                    {{ search_form.role|add_class:'form-select' }}
                </div>
                <div class="col-md-2">
                    {{ search_form.is_active.label_tag }}
                    {{ search_form.is_active|add_class:'form-select' }}
                </div>
                <div class="col-md-2 align-self-end">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                </div>
            </form>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Department</th>
                    <th>Role</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for user in page_obj %}
                <tr>
                    <td>{{ user.get_full_name }}</td>
                    <td>{{ user.email }}</td>
                    <td>{{ user.profile.department|default:'—' }}</td>
                    <td>{{ user.profile.role|title|default:'—' }}</td>
                    <td>
                        {% if user.is_active %}
                            <span class="badge bg-success">Active</span>
                        {% else %}
                            <span class="badge bg-danger">Inactive</span>
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr><td colspan="5" class="text-center">No users found.</td></tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <nav aria-label="User pagination">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a></li>
            {% else %}
                <li class="page-item disabled"><span class="page-link">Previous</span></li>
            {% endif %}
            <li class="page-item disabled"><span class="page-link">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span></li>
            {% if page_obj.has_next %}
                <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a></li>
            {% else %}
                <li class="page-item disabled"><span class="page-link">Next</span></li>
            {% endif %}
        </ul>
    </nav>
    <div class="mt-5">
        <h4>Recent User Audit Logs</h4>
        <ul class="list-group">
            {% for log in audit_logs %}
                <li class="list-group-item small">
                    <strong>{{ log.user.get_full_name|default:log.user.username }}</strong> - {{ log.action }} ({{ log.timestamp|date:'SHORT_DATETIME_FORMAT' }})
                </li>
            {% empty %}
                <li class="list-group-item">No recent audit logs.</li>
            {% endfor %}
        </ul>
    </div>
</div>
{% endblock %}
