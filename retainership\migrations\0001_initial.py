# Generated by Django 5.2 on 2025-07-04 15:31

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('patients', '0004_alter_patient_phone_number'),
    ]

    operations = [
        migrations.CreateModel(
            name='RetainershipPatient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('retainership_reg_number', models.BigIntegerField(help_text='Unique Retainership registration number starting with 3, 10 digits long', unique=True, validators=[django.core.validators.MinValueValidator(**********), django.core.validators.MaxValueValidator(**********)])),
                ('is_active', models.<PERSON>olean<PERSON>ield(default=True)),
                ('date_registered', models.DateTimeField(auto_now_add=True)),
                ('patient', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='retainership_info', to='patients.patient')),
            ],
            options={
                'verbose_name': 'Retainership Patient',
                'verbose_name_plural': 'Retainership Patients',
            },
        ),
    ]
