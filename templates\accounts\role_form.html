{% extends 'core/base.html' %}
{% load crispy_forms_tags %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ page_title }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Role Details</h6>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                {{ form|crispy }}
                <button type="submit" class="btn btn-primary">Save</button>
                <a href="{% url 'accounts:role_management' %}" class="btn btn-secondary">Cancel</a>
            </form>
        </div>
    </div>
</div>
{% endblock %}