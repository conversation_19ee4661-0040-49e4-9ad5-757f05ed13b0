{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        {% if form.initial.patient or selected_patient or request.GET.patient %}
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Patient</label>
                                <input type="hidden" name="patient" value="{{ form.initial.patient|default:selected_patient.id|default:request.GET.patient }}">
                                <div class="form-control-plaintext">
                                    {{ selected_patient.get_full_name|default:selected_patient|default:form.initial.patient|default:request.GET.patient }}
                                </div>
                            </div>
                        {% else %}
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.patient.id_for_label }}" class="form-label">Patient</label>
                                {{ form.patient|add_class:"form-select select2" }}
                                {% if form.patient.errors %}
                                    <div class="text-danger">
                                        {{ form.patient.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.doctor.id_for_label }}" class="form-label">Doctor</label>
                            {{ form.doctor|add_class:"form-select select2" }}
                            {% if form.doctor.errors %}
                                <div class="text-danger">
                                    {{ form.doctor.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.prescription_date.id_for_label }}" class="form-label">Prescription Date</label>
                            {{ form.prescription_date|add_class:"form-control" }}
                            {% if form.prescription_date.errors %}
                                <div class="text-danger">
                                    {{ form.prescription_date.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.diagnosis.id_for_label }}" class="form-label">Diagnosis</label>
                            {{ form.diagnosis|add_class:"form-control" }}
                            {% if form.diagnosis.errors %}
                                <div class="text-danger">
                                    {{ form.diagnosis.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                            {{ form.status|add_class:"form-select" }}
                            {% if form.status.errors %}
                                <div class="text-danger">
                                    {{ form.status.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.prescription_type.id_for_label }}" class="form-label">Prescription Type</label>
                            {{ form.prescription_type|add_class:"form-select" }}
                            {% if form.prescription_type.errors %}
                                <div class="text-danger">
                                    {{ form.prescription_type.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                            {{ form.notes|add_class:"form-control" }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {{ form.notes.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'pharmacy:prescriptions' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Prescriptions
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Prescription
                        </button>
                    </div>
                </form>
                {% if form.instance.pk %}
                    <div class="d-flex justify-content-end mt-3 flex-wrap gap-2">
                        {# Invoice is generated automatically when dispensing #}
                        {% if form.instance.invoice %}
                            <a href="{% url 'billing:invoice_detail' form.instance.invoice.pk %}" class="btn btn-success ms-2">
                                <i class="fas fa-file-invoice-dollar"></i> View Invoice
                            </a>
                            {% if form.instance.invoice.status != 'paid' %}
                                <a href="{% url 'billing:payment' form.instance.invoice.pk %}" class="btn btn-danger ms-2">
                                    <i class="fas fa-money-bill-wave"></i> Record Payment
                                </a>
                            {% endif %}
                        {% endif %}
                        <a href="{% url 'pharmacy:dispense_prescription' form.instance.pk %}" class="btn btn-primary ms-2">
                            <i class="fas fa-pills"></i> Dispense Medications
                        </a>
                    </div>
                    <div class="alert alert-info mt-2">
                        <i class="fas fa-info-circle me-2"></i>
                        Invoice will be generated automatically when you dispense medications.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for patient and doctor dropdowns
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
