{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Manage Doctors - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Manage Doctors</h1>
        <a href="{% url 'doctors:add_doctor' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle mr-1"></i> Add New Doctor
        </a>
    </div>

    <!-- Doctors List -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">All Doctors</h6>
        </div>
        <div class="card-body">
            {% if doctors %}
                <div class="table-responsive">
                    <table class="table table-bordered" id="doctorsTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Specialization</th>
                                <th>Department</th>
                                <th>License Number</th>
                                <th>Experience</th>
                                <th>Available</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for doctor in doctors %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if doctor.user.profile.profile_picture %}
                                                <img src="{{ doctor.user.profile.profile_picture.url }}" alt="{{ doctor.get_full_name }}" class="img-profile rounded-circle mr-2" style="width: 40px; height: 40px; object-fit: cover;">
                                            {% else %}
                                                <img src="/static/img/undraw_profile.svg" alt="{{ doctor.get_full_name }}" class="img-profile rounded-circle mr-2" style="width: 40px; height: 40px; object-fit: cover;">
                                            {% endif %}
                                            <div>
                                                <div>{{ doctor.get_full_name }}</div>
                                                <div class="small text-muted">{{ doctor.user.email }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ doctor.specialization.name }}</td>
                                    <td>{{ doctor.department.name }}</td>
                                    <td>{{ doctor.license_number }}</td>
                                    <td>{{ doctor.get_experience_display_value }}</td>
                                    <td>
                                        {% if doctor.available_for_appointments %}
                                            <span class="badge bg-success text-white">Yes</span>
                                        {% else %}
                                            <span class="badge bg-danger text-white">No</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'doctors:doctor_detail' doctor.id %}" class="btn btn-info btn-sm" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'doctors:edit_doctor' doctor.id %}" class="btn btn-primary btn-sm" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'doctors:delete_doctor' doctor.id %}" class="btn btn-danger btn-sm" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <p class="text-muted mb-0">No doctors found in the system.</p>
                    <a href="{% url 'doctors:add_doctor' %}" class="btn btn-primary mt-3">
                        <i class="fas fa-plus-circle mr-1"></i> Add New Doctor
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#doctorsTable').DataTable({
            "order": [[0, "asc"]]
        });
    });
</script>
{% endblock %}
