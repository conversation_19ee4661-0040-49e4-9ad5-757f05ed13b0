{% extends "base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">{{ title }}</h3>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    {% if form_errors %}
                        <div class="alert alert-danger">
                            <strong>Please correct the following errors:</strong>
                            <ul>
                                {% for field, errors in form_errors.items %}
                                    <li>{{ field }}:
                                        <ul>
                                            {% for error in errors %}
                                                <li>{{ error }}</li>
                                            {% endfor %}
                                        </ul>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        {% for field in form %}
                            <div class="mb-3">
                                {{ field.label_tag }}
                                {{ field }}
                                {% if field.help_text %}
                                    <div class="form-text">{{ field.help_text }}</div>
                                {% endif %}
                                {% for error in field.errors %}
                                    <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endfor %}
                        <button type="submit" class="btn btn-success">Submit</button>
                        <a href="{% url 'inpatient:admission_list' %}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}