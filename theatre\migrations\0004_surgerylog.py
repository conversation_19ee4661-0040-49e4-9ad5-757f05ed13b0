# Generated by Django 5.2 on 2025-06-27 17:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('theatre', '0003_alter_surgery_anesthetist_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SurgeryLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('event_type', models.CharField(max_length=100)),
                ('details', models.TextField(blank=True, null=True)),
                ('recorded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('surgery', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='theatre.surgery')),
            ],
            options={
                'verbose_name': 'Surgery Log Entry',
                'verbose_name_plural': 'Surgery Log Entries',
                'ordering': ['timestamp'],
            },
        ),
    ]
