{% extends 'base.html' %}

{% block title %}{{ medication.name }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Medication Details: {{ medication.name }}</h4>
                <div>
                    <a href="{% url 'pharmacy:edit_medication' medication.id %}" class="btn btn-light me-2">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{% url 'pharmacy:delete_medication' medication.id %}" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2 mb-3">Basic Information</h5>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Medication ID:</div>
                            <div class="col-md-8">{{ medication.id }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Name:</div>
                            <div class="col-md-8">{{ medication.name }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Generic Name:</div>
                            <div class="col-md-8">{{ medication.generic_name }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Category:</div>
                            <div class="col-md-8">{{ medication.category.name }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Dosage Form:</div>
                            <div class="col-md-8">{{ medication.dosage_form }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Strength:</div>
                            <div class="col-md-8">{{ medication.strength }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Description:</div>
                            <div class="col-md-8">{{ medication.description }}</div>
                        </div>
                    </div>
                    
                    <!-- Inventory Information -->
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2 mb-3">Inventory Information</h5>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Manufacturer:</div>
                            <div class="col-md-8">{{ medication.manufacturer }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Price:</div>
                            <div class="col-md-8">₦{{ medication.price }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Stock Quantity:</div>
                            <div class="col-md-8">
                                {% if medication.stock_quantity == 0 %}
                                    <span class="badge bg-danger">Out of Stock</span>
                                {% elif medication.stock_quantity <= medication.reorder_level %}
                                    <span class="badge bg-warning">Low Stock ({{ medication.stock_quantity }})</span>
                                {% else %}
                                    <span class="badge bg-success">{{ medication.stock_quantity }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Reorder Level:</div>
                            <div class="col-md-8">{{ medication.reorder_level }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Expiry Date:</div>
                            <div class="col-md-8">
                                {% if medication.expiry_date %}
                                    {{ medication.expiry_date|date:"F d, Y" }}
                                    {% if medication.expiry_date < today %}
                                        <span class="badge bg-danger">Expired</span>
                                    {% elif medication.expiry_date < expiry_warning %}
                                        <span class="badge bg-warning">Expiring Soon</span>
                                    {% endif %}
                                {% else %}
                                    Not specified
                                {% endif %}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Status:</div>
                            <div class="col-md-8">
                                {% if medication.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Additional Information -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5 class="border-bottom pb-2 mb-3">Additional Information</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Side Effects</h6>
                                    </div>
                                    <div class="card-body">
                                        {{ medication.side_effects|default:"No side effects information available." }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Precautions</h6>
                                    </div>
                                    <div class="card-body">
                                        {{ medication.precautions|default:"No precautions information available." }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Storage Instructions</h6>
                                    </div>
                                    <div class="card-body">
                                        {{ medication.storage_instructions|default:"No storage instructions available." }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Purchase History -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5 class="border-bottom pb-2 mb-3">Purchase History</h5>
                        {% if purchase_items %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Purchase Date</th>
                                            <th>Invoice #</th>
                                            <th>Supplier</th>
                                            <th>Quantity</th>
                                            <th>Unit Price</th>
                                            <th>Total Price</th>
                                            <th>Batch #</th>
                                            <th>Expiry Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in purchase_items %}
                                            <tr>
                                                <td>{{ item.purchase.purchase_date|date:"M d, Y" }}</td>
                                                <td>{{ item.purchase.invoice_number }}</td>
                                                <td>{{ item.purchase.supplier.name }}</td>
                                                <td>{{ item.quantity }}</td>
                                                <td>₦{{ item.unit_price }}</td>
                                                <td>₦{{ item.total_price }}</td>
                                                <td>{{ item.batch_number }}</td>
                                                <td>
                                                    {{ item.expiry_date|date:"M d, Y" }}
                                                    {% if item.expiry_date < today %}
                                                        <span class="badge bg-danger">Expired</span>
                                                    {% elif item.expiry_date < expiry_warning %}
                                                        <span class="badge bg-warning">Expiring Soon</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No purchase history available for this medication.
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Prescription History -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5 class="border-bottom pb-2 mb-3">Prescription History</h5>
                        {% if prescription_items %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Patient</th>
                                            <th>Doctor</th>
                                            <th>Dosage</th>
                                            <th>Frequency</th>
                                            <th>Duration</th>
                                            <th>Quantity</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in prescription_items %}
                                            <tr>
                                                <td>{{ item.prescription.prescription_date|date:"M d, Y" }}</td>
                                                <td>
                                                    <a href="{% url 'patients:detail' item.prescription.patient.id %}">
                                                        {{ item.prescription.patient.get_full_name }}
                                                    </a>
                                                </td>
                                                <td>Dr. {{ item.prescription.doctor.get_full_name }}</td>
                                                <td>{{ item.dosage }}</td>
                                                <td>{{ item.frequency }}</td>
                                                <td>{{ item.duration }}</td>
                                                <td>{{ item.quantity }}</td>
                                                <td>
                                                    {% if item.is_dispensed %}
                                                        <span class="badge bg-success">Dispensed</span>
                                                    {% else %}
                                                        <span class="badge bg-warning">Pending</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No prescription history available for this medication.
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'pharmacy:inventory' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Inventory
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
