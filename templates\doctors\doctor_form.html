{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Add Doctor - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Add New Doctor</h1>
        <a href="{% url 'doctors:manage_doctors' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-1"></i> Back to Doctors
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Doctor Information</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="text-gray-800 mb-3">Account Information</h5>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ user_form.first_name.id_for_label }}" class="form-label">First Name *</label>
                        {{ user_form.first_name|add_class:"form-control" }}
                        {% if user_form.first_name.errors %}
                            <div class="text-danger">{{ user_form.first_name.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ user_form.last_name.id_for_label }}" class="form-label">Last Name *</label>
                        {{ user_form.last_name|add_class:"form-control" }}
                        {% if user_form.last_name.errors %}
                            <div class="text-danger">{{ user_form.last_name.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ user_form.username.id_for_label }}" class="form-label">Username *</label>
                        {{ user_form.username|add_class:"form-control" }}
                        {% if user_form.username.errors %}
                            <div class="text-danger">{{ user_form.username.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ user_form.email.id_for_label }}" class="form-label">Email *</label>
                        {{ user_form.email|add_class:"form-control" }}
                        {% if user_form.email.errors %}
                            <div class="text-danger">{{ user_form.email.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ user_form.phone_number.id_for_label }}" class="form-label">Phone Number *</label>
                        {{ user_form.phone_number|add_class:"form-control" }}
                        <small class="form-text text-muted">{{ user_form.phone_number.help_text }}</small>
                        {% if user_form.phone_number.errors %}
                            <div class="text-danger">{{ user_form.phone_number.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ user_form.password1.id_for_label }}" class="form-label">Password *</label>
                        {{ user_form.password1|add_class:"form-control" }}
                        {% if user_form.password1.errors %}
                            <div class="text-danger">{{ user_form.password1.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ user_form.password2.id_for_label }}" class="form-label">Confirm Password *</label>
                        {{ user_form.password2|add_class:"form-control" }}
                        {% if user_form.password2.errors %}
                            <div class="text-danger">{{ user_form.password2.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="text-gray-800 mb-3">Professional Information</h5>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ doctor_form.specialization.id_for_label }}" class="form-label">Specialization *</label>
                        {{ doctor_form.specialization|add_class:"form-select" }}
                        {% if doctor_form.specialization.errors %}
                            <div class="text-danger">{{ doctor_form.specialization.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ doctor_form.department.id_for_label }}" class="form-label">Department *</label>
                        {{ doctor_form.department|add_class:"form-select" }}
                        {% if doctor_form.department.errors %}
                            <div class="text-danger">{{ doctor_form.department.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ doctor_form.license_number.id_for_label }}" class="form-label">License Number *</label>
                        {{ doctor_form.license_number|add_class:"form-control" }}
                        {% if doctor_form.license_number.errors %}
                            <div class="text-danger">{{ doctor_form.license_number.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ doctor_form.experience.id_for_label }}" class="form-label">Experience *</label>
                        {{ doctor_form.experience|add_class:"form-select" }}
                        {% if doctor_form.experience.errors %}
                            <div class="text-danger">{{ doctor_form.experience.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ doctor_form.qualification.id_for_label }}" class="form-label">Qualification *</label>
                        {{ doctor_form.qualification|add_class:"form-control" }}
                        {% if doctor_form.qualification.errors %}
                            <div class="text-danger">{{ doctor_form.qualification.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ doctor_form.consultation_fee.id_for_label }}" class="form-label">Consultation Fee *</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            {{ doctor_form.consultation_fee|add_class:"form-control" }}
                        </div>
                        {% if doctor_form.consultation_fee.errors %}
                            <div class="text-danger">{{ doctor_form.consultation_fee.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-12 mb-3">
                        <label for="{{ doctor_form.bio.id_for_label }}" class="form-label">Bio</label>
                        {{ doctor_form.bio|add_class:"form-control" }}
                        {% if doctor_form.bio.errors %}
                            <div class="text-danger">{{ doctor_form.bio.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="{{ doctor_form.signature.id_for_label }}" class="form-label">Signature</label>
                        {{ doctor_form.signature|add_class:"form-control" }}
                        {% if doctor_form.signature.errors %}
                            <div class="text-danger">{{ doctor_form.signature.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="form-check mt-4">
                            {{ doctor_form.available_for_appointments }}
                            <label class="form-check-label" for="{{ doctor_form.available_for_appointments.id_for_label }}">
                                Available for Appointments
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> Save Doctor
                    </button>
                    <a href="{% url 'doctors:manage_doctors' %}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
