{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">Radiology Results for {{ patient.get_full_name }}</h2>
    <div class="card mb-4 border-primary">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <strong><i class="fas fa-user"></i> Patient Information</strong>
            <span class="badge badge-light text-primary font-weight-bold">
                ID: {{ patient.patient_number }}
            </span>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-2">
                        <strong><i class="fas fa-id-card text-primary"></i> Patient Number:</strong>
                        <span class="text-primary font-weight-bold">{{ patient.patient_number }}</span>
                    </p>
                    <p class="mb-2">
                        <strong><i class="fas fa-calendar text-info"></i> Date of Birth:</strong>
                        {{ patient.date_of_birth|date:"F d, Y" }}
                    </p>
                    <p class="mb-2">
                        <strong><i class="fas fa-venus-mars text-secondary"></i> Gender:</strong>
                        {{ patient.get_gender_display }}
                    </p>
                </div>
                <div class="col-md-6">
                    {% if patient.phone_number %}
                    <p class="mb-2">
                        <strong><i class="fas fa-phone text-success"></i> Phone:</strong>
                        {{ patient.phone_number }}
                    </p>
                    {% endif %}
                    {% if patient.email %}
                    <p class="mb-2">
                        <strong><i class="fas fa-envelope text-warning"></i> Email:</strong>
                        {{ patient.email }}
                    </p>
                    {% endif %}
                    <p class="mb-2">
                        <strong><i class="fas fa-clock text-muted"></i> Age:</strong>
                        {% if patient.date_of_birth %}
                        {{ patient.date_of_birth|timesince }} old
                        {% else %}
                        Not specified
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
    <div class="card">
        <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
            <strong><i class="fas fa-x-ray"></i> Radiology Results</strong>
            <span class="badge badge-light text-secondary">
                Total Results: {{ results|length }}
            </span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="thead-dark">
                        <tr>
                            <th><i class="fas fa-calendar"></i> Date</th>
                            <th><i class="fas fa-user"></i> Patient ID</th>
                            <th><i class="fas fa-stethoscope"></i> Test</th>
                            <th><i class="fas fa-search"></i> Findings</th>
                            <th><i class="fas fa-clipboard-check"></i> Impression</th>
                            <th><i class="fas fa-user-md"></i> Performed By</th>
                            <th><i class="fas fa-image"></i> Image</th>
                            <th><i class="fas fa-cogs"></i> Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for result in results %}
                        <tr>
                            <td>
                                <span class="text-primary font-weight-bold">
                                    {{ result.result_date|date:'M d, Y' }}
                                </span><br>
                                <small class="text-muted">{{ result.result_date|date:'H:i' }}</small>
                            </td>
                            <td>
                                <span class="badge badge-primary">
                                    {{ patient.patient_number }}
                                </span>
                            </td>
                            <td>
                                <strong>{{ result.order.test.name }}</strong>
                                {% if result.order.priority %}
                                <br><span class="badge badge-warning badge-sm">{{ result.order.get_priority_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 200px;" title="{{ result.findings }}">
                                    {{ result.findings|truncatewords:10 }}
                                </div>
                                {% if result.findings|length > 50 %}
                                <small><a href="#" onclick="showFullText('{{ result.id }}', 'findings')">Read more...</a></small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 150px;" title="{{ result.impression }}">
                                    {{ result.impression|truncatewords:8 }}
                                </div>
                                {% if result.impression|length > 40 %}
                                <small><a href="#" onclick="showFullText('{{ result.id }}', 'impression')">Read more...</a></small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="text-info">
                                    {{ result.performed_by.get_full_name|default:result.performed_by.username }}
                                </span>
                            </td>
                            <td>
                                {% if result.image_file %}
                                    <a href="{{ result.image_file.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                {% else %}
                                    <span class="text-muted">No Image</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'radiology:result_detail' result.id %}" class="btn btn-sm btn-info" title="View Details">
                                        <i class="fas fa-info-circle"></i>
                                    </a>
                                    {% if result.image_file %}
                                    <a href="{{ result.image_file.url }}" download class="btn btn-sm btn-success" title="Download Image">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center text-muted py-4">
                                <i class="fas fa-search fa-2x mb-2"></i><br>
                                No radiology results found for this patient.
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Quick Access Summary -->
    {% if results %}
    <div class="card mt-4">
        <div class="card-header bg-info text-white">
            <strong><i class="fas fa-chart-bar"></i> Quick Access Summary</strong>
        </div>
        <div class="card-body">
            <div class="row text-center">
                <div class="col-md-3">
                    <h4 class="text-primary">{{ patient.patient_number }}</h4>
                    <small class="text-muted">Patient Number</small>
                </div>
                <div class="col-md-3">
                    <h4 class="text-success">{{ results|length }}</h4>
                    <small class="text-muted">Total Results</small>
                </div>
                <div class="col-md-3">
                    <h4 class="text-info">{{ results.0.result_date|date:"M Y" }}</h4>
                    <small class="text-muted">Latest Result</small>
                </div>
                <div class="col-md-3">
                    <h4 class="text-warning">{{ patient.get_full_name|truncatewords:2 }}</h4>
                    <small class="text-muted">Patient Name</small>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal for Full Text Display -->
<div class="modal fade" id="fullTextModal" tabindex="-1" role="dialog" aria-labelledby="fullTextModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fullTextModalLabel">Full Text</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="fullTextContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showFullText(resultId, fieldType) {
    // This would typically make an AJAX call to get the full text
    // For now, we'll show a placeholder
    const modal = $('#fullTextModal');
    const content = $('#fullTextContent');

    modal.find('.modal-title').text(`Full ${fieldType.charAt(0).toUpperCase() + fieldType.slice(1)} - Result #${resultId}`);
    content.html(`<p>Loading full ${fieldType}...</p>`);
    modal.modal('show');

    // In a real implementation, you would make an AJAX call here:
    // $.get(`/radiology/result/${resultId}/${fieldType}/`, function(data) {
    //     content.html(data);
    // });
}

// Print functionality
function printResults() {
    window.print();
}

// Add print button if not exists
$(document).ready(function() {
    if ($('.print-btn').length === 0) {
        $('h2').after(`
            <div class="mb-3">
                <button onclick="printResults()" class="btn btn-outline-primary print-btn">
                    <i class="fas fa-print"></i> Print Results
                </button>
                <button onclick="window.history.back()" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back
                </button>
            </div>
        `);
    }
});
</script>
{% endblock %}
