{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2>{{ title }}</h2>

    <div class="card mb-4">
        <div class="card-header">
            Add New Dispensary
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                {{ form.as_p }}
                <button type="submit" class="btn btn-primary">Add Dispensary</button>
            </form>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            Existing Dispensaries
        </div>
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Location</th>
                        <th>Contact Person</th>
                        <th>Active</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for dispensary in dispensaries %}
                        <tr>
                            <td>{{ dispensary.name }}</td>
                            <td>{{ dispensary.location|default:"N/A" }}</td>
                            <td>{{ dispensary.contact_person.get_full_name|default:"N/A" }}</td>
                            <td>{{ dispensary.is_active|yesno:"Yes,No" }}</td>
                            <td>
                                <a href="{% url 'pharmacy:edit_dispensary' dispensary.id %}" class="btn btn-sm btn-warning">Edit</a>
                                <a href="{% url 'pharmacy:delete_dispensary' dispensary.id %}" class="btn btn-sm btn-danger">Delete</a>
                                <a href="{% url 'pharmacy:dispensary_inventory' dispensary.id %}" class="btn btn-sm btn-info">Inventory</a>
                                <a href="{% url 'pharmacy:dispensary_dispensing_report' dispensary.id %}" class="btn btn-sm btn-secondary">Dispensing Report</a>
                            </td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="5">No dispensaries found.</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}