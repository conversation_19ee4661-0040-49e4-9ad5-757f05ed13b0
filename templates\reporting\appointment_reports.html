{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ title }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Appointments</h6>
        </div>
        <div class="card-body">
            <form method="get">
                <div class="row">
                    <div class="col-md-3">{{ form.start_date.label_tag }} {{ form.start_date }}</div>
                    <div class="col-md-3">{{ form.end_date.label_tag }} {{ form.end_date }}</div>
                    <div class="col-md-3">{{ form.doctor.label_tag }} {{ form.doctor }}</div>
                    <div class="col-md-3">{{ form.status.label_tag }} {{ form.status }}</div>
                </div>
                <button type="submit" class="btn btn-primary mt-3">Filter</button>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Appointment List</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Patient</th>
                            <th>Doctor</th>
                            <th>Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for appointment in page_obj %}
                        <tr>
                            <td>{{ appointment.id }}</td>
                            <td>{{ appointment.patient.get_full_name }}</td>
                            <td>{{ appointment.doctor.get_full_name }}</td>
                            <td>{{ appointment.appointment_date }}</td>
                            <td>{{ appointment.get_status_display }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">No appointments found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% include 'pagination.html' %}
        </div>
    </div>
</div>
{% endblock %}