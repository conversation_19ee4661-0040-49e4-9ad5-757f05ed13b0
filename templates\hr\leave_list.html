{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-2 text-gray-800">{{ title }}</h1>
    <p class="mb-4">A list of all leave requests in the system.</p>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="m-0 font-weight-bold text-primary">{{ total_leaves }} Leave Requests</h6>
                </div>
                <div class="col-md-6 text-right">
                    <a href="{% url 'hr:request_leave' %}" class="btn btn-primary btn-sm">Request Leave</a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form method="get" class="form-inline mb-3">
                {{ search_form.as_p }}
                <button type="submit" class="btn btn-primary">Search</button>
            </form>

            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Staff</th>
                            <th>Leave Type</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave in page_obj %}
                        <tr>
                            <td>{{ leave.staff.get_full_name }}</td>
                            <td>{{ leave.get_leave_type_display }}</td>
                            <td>{{ leave.start_date }}</td>
                            <td>{{ leave.end_date }}</td>
                            <td><span class="badge badge-{{ leave.status|lower }}">{{ leave.get_status_display }}</span></td>
                            <td>
                                {% if leave.status == 'pending' and request.user.is_superuser %}
                                <a href="{% url 'hr:approve_leave' leave.id %}" class="btn btn-success btn-sm">Approve</a>
                                <a href="{% url 'hr:reject_leave' leave.id %}" class="btn btn-danger btn-sm">Reject</a>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6">No leave requests found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                    <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">&laquo;</a></li>
                    {% else %}
                    <li class="page-item disabled"><a class="page-link" href="#">&laquo;</a></li>
                    {% endif %}

                    {% for i in page_obj.paginator.page_range %}
                    {% if page_obj.number == i %}
                    <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                    {% else %}
                    <li class="page-item"><a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ i }}</a></li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">&raquo;</a></li>
                    {% else %}
                    <li class="page-item disabled"><a class="page-link" href="#">&raquo;</a></li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}