/* === HMS Standalone Sidebar CSS (Context7 MCP Best Practice) === */

/* 1. Sidebar Container */
#sidebar, .sidebar {
    width: 14rem; /* Adjusted width */
    min-width: 14rem;
    max-width: 14rem;
    height: 100vh;
    min-height: 100vh;
    background: #23272b;
    color: #fff;
    position: relative;
    left: 0;
    top: 0;
    margin: 0;
    padding: 0;
    z-index: 100;
    display: flex;
    flex-direction: column;
    font-family: 'Segoe UI', Arial, sans-serif;
    overflow-y: auto;
    background-attachment: local;
    transition: all 0.3s; /* Added transition for smooth toggle */
}

/* 2. Sidebar Branding */
.sidebar .sidebar-brand, #sidebar .sidebar-brand {
    padding: 24px 0 16px 0;
    text-align: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #00bfff;
    letter-spacing: 1px;
}

/* 3. Sidebar Navigation */
.sidebar .nav, #sidebar .nav {
    flex: 1 1 0%;
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;
    list-style: none;
}
.sidebar .nav li, #sidebar .nav li {
    width: 100%;
}
.sidebar .nav a, #sidebar .nav a {
    color: #fff;
    text-decoration: none;
    padding: 12px 24px;
    display: block;
    transition: background 0.2s, color 0.2s;
    border-left: 4px solid transparent;
}
.sidebar .nav a.active, #sidebar .nav a.active, .sidebar .nav a:hover, #sidebar .nav a:hover {
    background: #1a1d21;
    color: #00bfff;
    border-left: 4px solid #00bfff;
}

/* 4. Sidebar Section Titles */
.sidebar .sidebar-heading, #sidebar .sidebar-heading {
    padding: 16px 24px 8px 24px;
    font-size: 0.85rem;
    text-transform: uppercase;
    color: #b0b3b8;
    letter-spacing: 1px;
    font-weight: 600;
}

/* 5. Sidebar Divider */
.sidebar hr, #sidebar hr {
    border: 0;
    border-top: 1px solid #444950;
    margin: 8px 0;
}

/* 6. Sidebar Footer */
.sidebar .sidebar-footer, #sidebar .sidebar-footer {
    padding: 16px 24px;
    font-size: 0.9rem;
    color: #b0b3b8;
    border-top: 1px solid #444950;
}

/* 7. Responsive: Stack Sidebar on Small Screens */
@media (max-width: 768px) { /* Changed to 768px for consistency with JS */
    #sidebar, .sidebar {
        position: fixed; /* Fixed position for overlay */
        left: 0;
        top: 0;
        height: 100vh;
        min-height: 100vh;
        width: 250px; /* Default width for mobile sidebar */
        transform: translateX(-100%); /* Hidden by default */
        box-shadow: 2px 0 8px rgba(0,0,0,0.08);
        background: linear-gradient(180deg, #23272b 80%, #1a1d21 100%);
        background-repeat: repeat-y;
        background-attachment: local;
        overflow-y: auto;
        z-index: 1050; /* Higher z-index for overlay */
    }

    .sidebar-open { /* Class added by JS when sidebar is open */
        transform: translateX(0%);
    }

    .sidebar-overlay { /* Overlay for when sidebar is open on mobile */
        display: none;
        position: fixed;
        top: 0; left: 0; right: 0; bottom: 0;
        background: rgba(0,0,0,0.3);
        z-index: 1040;
    }

    .sidebar-overlay.active {
        display: block;
    }

    .sidebar-toggle-btn { /* Button to toggle sidebar on mobile */
        display: inline-block; /* Show button on mobile */
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #23272b;
        margin-right: 1rem;
    }

    /* Adjust content area when sidebar is open on mobile */
    body.sidebar-open #content-wrapper {
        margin-left: 250px; /* Shift content to the right */
    }
}

/* 8. Accessibility: Focus Styles */
.sidebar a:focus, #sidebar a:focus {
    outline: 2px solid #00bfff;
    outline-offset: 2px;
}

/* Sidebar Toggler for desktop */
.sidebar-toggled #sidebar {
    width: 6.5rem !important; /* Collapsed width */
    min-width: 6.5rem !important;
    max-width: 6.5rem !important;
    overflow: hidden;
}

.sidebar-toggled .sidebar .sidebar-brand .sidebar-brand-text {
    display: none;
}

.sidebar-toggled .sidebar .nav-item .nav-link {
    text-align: center;
    padding: 0.75rem 1rem;
    width: 6.5rem;
}

.sidebar-toggled .sidebar .nav-item .nav-link span {
    font-size: 0.65rem;
    display: block;
}

.sidebar-toggled .sidebar .nav-item .nav-link i {
    margin-right: 0;
    font-size: 1rem;
}

.sidebar-toggled .sidebar .nav-item .nav-link[data-bs-toggle="collapse"]::after {
    display: none;
}

.sidebar-toggled .sidebar .nav-item .collapse {
    position: absolute;
    left: calc(6.5rem + 1.5rem / 2);
    z-index: 1;
    top: 2px;
    animation-name: growIn;
    animation-duration: 200ms;
    animation-timing-function: transform cubic-bezier(0.18, 1.25, 0.4, 1), opacity cubic-bezier(0, 1, 0.4, 1);
}

.sidebar-toggled .sidebar .nav-item .collapse .collapse-inner {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.sidebar-toggled .sidebar .nav-item .collapsing {
    display: none;
    transition: none;
}

.sidebar-toggled .sidebar .nav-item:last-child {
    margin-bottom: 1rem;
}

.sidebar-toggled .sidebar .nav-item .collapse,
.sidebar-toggled .sidebar .nav-item .collapsing {
    margin: 0;
}

.sidebar-toggled .sidebar .nav-item .collapse .collapse-inner,
.sidebar-toggled .sidebar .nav-item .collapsing .collapse-inner {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.sidebar-toggled .sidebar .collapse::before {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    right: 100%;
    height: 100%;
    width: 1.5rem;
}

/* Content Wrapper adjustments for desktop sidebar toggle */
body.sidebar-toggled #content-wrapper {
    margin-left: 6.5rem; /* Shift content to the right when sidebar is collapsed */
}

#sidebarToggle {
    width: 2.5rem;
    height: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

#sidebarToggle::after {
    font-weight: 900;
    content: '\f104';
    font-family: 'Font Awesome 5 Free';
    margin-right: 0.1rem;
}

#sidebarToggle:hover {
    background-color: rgba(255, 255, 255, 0.25);
    color: #ffffff;
}

.sidebar-toggled #sidebarToggle::after {
    content: '\f105';
    font-family: 'Font Awesome 5 Free';
    margin-left: 0.25rem;
}

/* Content Wrapper */
#content-wrapper {
    background-color: #f8f9fc;
    width: 100%;
    overflow-x: hidden;
    margin-left: 14rem; /* Default margin for expanded sidebar */
    transition: margin-left 0.3s; /* Smooth transition for margin */
}

#content-wrapper #content {
    flex: 1 0 auto;
}

/* Page Wrapper */
#wrapper {
    display: flex;
    flex-direction: row;
    min-height: 100vh;
}

/* Responsive adjustments for smaller screens where sidebar might behave differently */
@media (max-width: 767.98px) { /* Bootstrap 5 breakpoint for sm */
    #content-wrapper {
        margin-left: 0; /* No margin on small screens, sidebar is overlay */
        width: 100%;
    }
}

#wrapper #content-wrapper #content {
    flex: 1 0 auto;
}

/* Responsive Styles for desktop sidebar */
@media (min-width: 768px) {
    .sidebar .nav-item .collapse {
        position: relative;
        left: 0;
        z-index: 1;
        top: 0;
        animation: none;
    }

    .sidebar .nav-item .collapse .collapse-inner {
        border-radius: 0;
        box-shadow: none;
    }

    .sidebar .nav-item .collapsing {
        display: block;
        transition: height 0.15s ease;
    }

    .sidebar .nav-item .collapse,
    .sidebar .nav-item .collapsing {
        margin: 0 0 1rem 0;
    }

    .sidebar .nav-item .nav-link {
        display: block;
        width: 100%;
        text-align: left;
        padding: 1rem;
        width: 14rem;
    }

    .sidebar .nav-item .nav-link i {
        font-size: 0.85rem;
        margin-right: 0.25rem;
    }

    .sidebar .nav-item .nav-link span {
        font-size: 0.85rem;
        display: inline;
    }

    .sidebar .nav-item .nav-link[data-bs-toggle="collapse"]::after {
        width: 1rem;
        text-align: center;
        float: right;
        vertical-align: 0;
        border: 0;
        font-weight: 900;
        content: '\f107';
        font-family: 'Font Awesome 5 Free';
        color: rgba(255, 255, 255, 0.8);
    }

    .sidebar .nav-item .nav-link[data-bs-toggle="collapse"].collapsed::after {
        content: '\f105';
    }

    .sidebar .sidebar-brand .sidebar-brand-icon i {
        font-size: 2rem;
    }

    .sidebar .sidebar-brand .sidebar-brand-text {
        display: inline;
    }

    .sidebar .sidebar-heading {
        text-align: left;
    }
}

/* Topbar Styles */
.topbar {
    height: 4.375rem;
}

.topbar #sidebarToggleTop {
    height: 2.5rem;
    width: 2.5rem;
}

.topbar #sidebarToggleTop:hover {
    background-color: #eaecf4;
}

.topbar #sidebarToggleTop:active {
    background-color: #dddfeb;
}

.topbar .navbar-search {
    width: 25rem;
}

.topbar .navbar-search input {
    font-size: 0.85rem;
    height: auto;
}

.topbar .topbar-divider {
    width: 0;
    border-right: 1px solid #e3e6f0;
    height: calc(4.375rem - 2rem);
    margin: auto 1rem;
}

.topbar .nav-item .nav-link {
    height: 4.375rem;
    display: flex;
    align-items: center;
    padding: 0 0.75rem;
}

.topbar .nav-item .nav-link:focus {
    outline: none;
}

.topbar .nav-item:focus {
    outline: none;
}

.topbar .dropdown {
    position: static;
}

.topbar .dropdown .dropdown-menu {
    width: calc(100% - 1.5rem);
    right: 0.75rem;
}

.topbar .dropdown-list {
    padding: 0;
    border: none;
    overflow: hidden;
}

.topbar .dropdown-list .dropdown-header {
    background-color: #4e73df;
    border: 1px solid #4e73df;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    color: #fff;
}

.topbar .dropdown-list .dropdown-item {
    white-space: normal;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    border-left: 1px solid #e3e6f0;
    border-right: 1px solid #e3e6f0;
    border-bottom: 1px solid #e3e6f0;
    line-height: 1.3rem;
}

.topbar .dropdown-list .dropdown-item .dropdown-list-image {
    position: relative;
    height: 2.5rem;
    width: 2.5rem;
}

.topbar .dropdown-list .dropdown-item .dropdown-list-image img {
    height: 2.5rem;
    width: 2.5rem;
}

.topbar .dropdown-list .dropdown-item .dropdown-list-image .status-indicator {
    background-color: #eaecf4;
    height: 0.75rem;
    width: 0.75rem;
    border-radius: 100%;
    position: absolute;
    bottom: 0;
    right: 0;
    border: 0.125rem solid #fff;
}

.topbar .dropdown-list .dropdown-item .text-truncate {
    max-width: 10rem;
}

.topbar .dropdown-list .dropdown-item:active {
    background-color: #eaecf4;
    color: #3a3b45;
}

@media (min-width: 576px) {
    .topbar .dropdown {
        position: relative;
    }
    .topbar .dropdown .dropdown-menu {
        width: auto;
        right: 0;
    }
    .topbar .dropdown-list {
        width: 20rem !important;
    }
    .topbar .dropdown-list .dropdown-item .text-truncate {
        max-width: 13.375rem;
    }
}

.topbar.navbar-dark .navbar-nav .nav-item .nav-link {
    color: rgba(255, 255, 255, 0.8);
}

.topbar.navbar-dark .navbar-nav .nav-item .nav-link:hover {
    color: #fff;
}

.topbar.navbar-dark .navbar-nav .nav-item .nav-link:active {
    color: #fff;
}

.topbar.navbar-light .navbar-nav .nav-item .nav-link {
    color: #d1d3e2;
}

.topbar.navbar-light .navbar-nav .nav-item .nav-link:hover {
    color: #b7b9cc;
}

.topbar.navbar-light .navbar-nav .nav-item .nav-link:active {
    color: #858796;
}

/* Main Content Styles */
.container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* Animations */
@keyframes growIn {
    0% {
        transform: scale(0.9);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.animated--grow-in {
    animation-name: growIn;
    animation-duration: 200ms;
    animation-timing-function: transform cubic-bezier(0.18, 1.25, 0.4, 1), opacity cubic-bezier(0, 1, 0.4, 1);
}

/* Gradient Colors */
.bg-gradient-primary {
    background-color: #4e73df;
    background-image: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
    background-size: cover;
    min-height: 100vh !important;
    height: 100vh !important;
    background-attachment: local !important;
}

/* Utilities */
.img-profile {
    height: 2rem;
    width: 2rem;
}

/* Footer Styles */
.sticky-footer {
    padding: 2rem 0;
    flex-shrink: 0;
}

.sticky-footer .copyright {
    line-height: 1;
    font-size: 0.8rem;
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    right: 1rem;
    bottom: 1rem;
    display: none;
    width: 2.75rem;
    height: 2.75rem;
    text-align: center;
    color: #fff;
    background: rgba(90, 92, 105, 0.5);
    line-height: 46px;
    border-radius: 0.35rem;
}

.scroll-to-top:focus, .scroll-to-top:hover {
    color: white;
}

.scroll-to-top:hover {
    background: #5a5c69;
}

.scroll-to-top i {
    font-weight: 800;
}

/* Strongly enforce sidebar/content layout for all screen sizes */
#wrapper {
    display: flex !important;
    flex-direction: row !important;
    min-height: 100vh;
    width: 100vw;
}

#content-wrapper {
    flex-grow: 1;
    width: 100%;
    min-width: 0;
    background: #f8f9fc;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: 100vh;
}
#content {
    width: 100%;
    flex: 1 1 auto;
}

