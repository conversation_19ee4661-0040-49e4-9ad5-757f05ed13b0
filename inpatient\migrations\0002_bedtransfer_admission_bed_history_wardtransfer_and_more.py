# Generated by Django 5.2 on 2025-06-27 16:38

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inpatient', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BedTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('notes', models.TextField(blank=True, null=True)),
                ('admission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bed_transfers', to='inpatient.admission')),
                ('from_bed', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_from', to='inpatient.bed')),
                ('to_bed', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_to', to='inpatient.bed')),
            ],
        ),
        migrations.AddField(
            model_name='admission',
            name='bed_history',
            field=models.ManyToManyField(related_name='admission_history', through='inpatient.BedTransfer', through_fields=('admission', 'to_bed'), to='inpatient.bed'),
        ),
        migrations.CreateModel(
            name='WardTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('notes', models.TextField(blank=True, null=True)),
                ('admission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ward_transfers', to='inpatient.admission')),
                ('from_ward', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_from', to='inpatient.ward')),
                ('to_ward', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_to', to='inpatient.ward')),
            ],
        ),
        migrations.AddField(
            model_name='admission',
            name='ward_history',
            field=models.ManyToManyField(related_name='admission_history', through='inpatient.WardTransfer', through_fields=('admission', 'to_ward'), to='inpatient.ward'),
        ),
    ]
