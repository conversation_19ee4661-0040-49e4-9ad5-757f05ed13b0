{% extends 'base.html' %}
{% load billing_tags %}

{% block title %}Invoice #{{ invoice.invoice_number }} - Hospital Management System{% endblock %}

{% block content %}
<div class="container mt-5 mb-5">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h2 class="mb-0">INVOICE</h2>
                            <p class="text-muted mb-0">#{{ invoice.invoice_number }}</p>
                            <p class="text-muted">Date: {{ invoice.created_at|date:"F d, Y" }}</p>
                        </div>
                        <div class="col-md-6 text-end">
                            <h3>Hospital Management System</h3>
                            <p>123 Hospital Street<br>City, State 12345<br>Phone: (*************<br>Email: <EMAIL></p>
                        </div>
                    </div>

                    <hr>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Bill To:</h5>
                            <p>
                                <strong>{{ invoice.patient.get_full_name }}</strong><br>
                                Patient ID: {{ invoice.patient.patient_id }}<br>
                                {% if invoice.patient.address %}{{ invoice.patient.address }}<br>{% endif %}
                                {% if invoice.patient.phone_number %}Phone: {{ invoice.patient.phone_number }}<br>{% endif %}
                                {% if invoice.patient.email %}Email: {{ invoice.patient.email }}{% endif %}
                            </p>
                        </div>
                        <div class="col-md-6 text-end">
                            <h5>Invoice Details:</h5>
                            <p>
                                <strong>Invoice Number:</strong> {{ invoice.invoice_number }}<br>
                                <strong>Invoice Date:</strong> {{ invoice.created_at|date:"F d, Y" }}<br>
                                <strong>Due Date:</strong> {{ invoice.due_date|date:"F d, Y" }}<br>
                                <strong>Status:</strong> {{ invoice.get_status_display }}
                            </p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Description</th>
                                            <th class="text-center">Quantity</th>
                                            <th class="text-end">Unit Price</th>
                                            <th class="text-end">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in invoice_items %}
                                            <tr>
                                                <td>{{ item.description }}</td>
                                                <td class="text-center">{{ item.quantity }}</td>
                                                <td class="text-end">{{ item.unit_price|currency }}</td>
                                                <td class="text-end">{{ item.total_price|currency }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="3" class="text-end">Subtotal:</th>
                                            <th class="text-end">{{ invoice.subtotal|currency }}</th>
                                        </tr>
                                        {% if invoice.tax_amount > 0 %}
                                        <tr>
                                            <th colspan="3" class="text-end">Tax:</th>
                                            <th class="text-end">{{ invoice.tax_amount|currency }}</th>
                                        </tr>
                                        {% endif %}
                                        {% if invoice.discount_amount > 0 %}
                                        <tr>
                                            <th colspan="3" class="text-end">Discount:</th>
                                            <th class="text-end">-{{ invoice.discount_amount|currency }}</th>
                                        </tr>
                                        {% endif %}
                                        <tr>
                                            <th colspan="3" class="text-end">Total:</th>
                                            <th class="text-end">{{ invoice.total_amount|currency }}</th>
                                        </tr>
                                        <tr>
                                            <th colspan="3" class="text-end">Amount Paid:</th>
                                            <th class="text-end">{{ invoice.amount_paid|currency }}</th>
                                        </tr>
                                        <tr>
                                            <th colspan="3" class="text-end">Balance Due:</th>
                                            <th class="text-end">{{ invoice.get_balance|currency }}</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    {% if invoice.notes %}
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h5>Notes:</h5>
                                <p>{{ invoice.notes }}</p>
                            </div>
                        </div>
                    {% endif %}

                    {% if payments %}
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h5>Payment History:</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Amount</th>
                                                <th>Method</th>
                                                <th>Reference</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for payment in payments %}
                                                <tr>
                                                    <td>{{ payment.payment_date|date:"M d, Y" }}</td>
                                                    <td>{{ payment.amount|currency }}</td>
                                                    <td>{{ payment.get_payment_method_display }}</td>
                                                    <td>{{ payment.reference_number|default:"-" }}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-12">
                            <p class="text-center mb-0">Thank you for choosing our hospital!</p>
                            <p class="text-center text-muted">For any questions regarding this invoice, please contact our billing department.</p>
                        </div>
                    </div>

                    {% if not print_view %}
                        <div class="row mt-4">
                            <div class="col-md-12 text-center">
                                <button onclick="window.print()" class="btn btn-primary">
                                    <i class="fas fa-print me-1"></i> Print Invoice
                                </button>
                                <a href="{% url 'billing:detail' invoice.id %}" class="btn btn-secondary ms-2">
                                    <i class="fas fa-arrow-left me-1"></i> Back to Invoice
                                </a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    @media print {
        body {
            font-size: 12pt;
        }

        .container {
            width: 100%;
            max-width: 100%;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .btn {
            display: none !important;
        }

        .navbar, .footer {
            display: none !important;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        {% if print_view %}
        window.print();
        {% endif %}
    });
</script>
{% endblock %}
