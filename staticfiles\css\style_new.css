/* Custom CSS for Hospital Management System */

/* General Styles */
body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #858796;
    text-align: left;
    background-color: #f8f9fc;
    overflow-x: hidden;
}

.container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* Navbar Styles */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
    transition: color 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #ffffff;
}

.navbar-dark .navbar-nav .active > .nav-link {
    font-weight: 600;
}

/* Card Styles */
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    margin-bottom: 20px;
    border: none;
    border-radius: 8px;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eaecf4;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Dashboard Icons */
.card i.fas {
    color: #007bff;
}

/* Form Styles */
.form-control {
    border-radius: 4px;
    border: 1px solid #d1d3e2;
    padding: 0.5rem 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Table Styles */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Button Styles */
.btn {
    border-radius: 4px;
    padding: 0.375rem 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.alert-primary {
    background-color: rgba(0, 123, 255, 0.15);
    color: #004085;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.15);
    color: #155724;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.15);
    color: #721c24;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.15);
    color: #856404;
}

/* Footer Styles */
.sticky-footer {
    padding: 2rem 0;
    flex-shrink: 0;
    background-color: #fff;
}

.sticky-footer .copyright {
    line-height: 1;
    font-size: 0.8rem;
    color: #858796;
}

.sticky-footer a {
    color: #4e73df;
    text-decoration: none;
    transition: color 0.3s ease;
}

.sticky-footer a:hover {
    color: #224abe;
    text-decoration: underline;
}

/* Login/Register Forms */
.auth-form {
    max-width: 450px;
    margin: 0 auto;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Patient Profile */
.patient-profile-img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #f8f9fa;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Dashboard Stats */
.stats-card {
    border-left: 4px solid #007bff;
}

.stats-icon {
    font-size: 2rem;
    opacity: 0.8;
}

/* Dropdown Menu Styles */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
}

.dropdown-item {
    padding: 0.5rem 1.5rem;
    transition: background-color 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.dropdown-divider {
    border-top: 1px solid #e9ecef;
}

/* Pagination Styles */
.pagination .page-link {
    color: #007bff;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #0056b3;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* Badge Styles */
.badge {
    font-weight: 600;
    padding: 0.35em 0.65em;
    border-radius: 0.5rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 15px;
    }

    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .sidebar {
        width: 0 !important;
    }

    .sidebar.toggled {
        width: 14rem !important;
    }

    .content {
        margin-left: 0;
    }

    .sidebar-toggled .content {
        margin-left: 0;
    }
}
