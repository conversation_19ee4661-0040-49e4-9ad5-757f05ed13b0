{"summary": {"total_operations": 25, "passed": 25, "failed": 0, "warnings": 0, "success_rate": 100.0}, "backup_location": "test_backups\\test_backup_20250802_090250", "cleaned_scripts": ["function_discovery.py", "comprehensive_function_tester.py", "view_url_tester.py", "api_integration_tester.py", "business_workflow_tester.py", "comprehensive_form_validator.py", "security_permission_tester.py", "performance_load_tester.py", "comprehensive_testing_summary.py"], "cleaned_reports": ["function_discovery_report.json", "comprehensive_test_report.json", "view_url_test_report.json", "api_integration_test_report.json", "business_workflow_test_report.json", "form_validation_test_report.json", "security_permission_test_report.json", "performance_load_test_report.json", "final_comprehensive_test_report.json"], "preserved_files": ["manage.py", "requirements.txt", "user_isolation_middleware.py", "user_isolation_examples.py", "test_script_cleanup.py"], "dependencies_found": [], "failed_cleanups": {"scripts": [], "reports": []}, "operations": [{"operation": "Backup-Creation", "category": "BACKUP", "status": "PASS", "message": "Backup created at test_backups\\test_backup_20250802_090250", "error": null, "details": null, "timestamp": "2025-08-02T09:02:50.980281"}, {"operation": "Dependency-Analysis", "category": "ANALYSIS", "status": "PASS", "message": "No dependencies found in main codebase", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.131972"}, {"operation": "Essential-Files-Preservation", "category": "PRESERVATION", "status": "PASS", "message": "Preserved 5 essential files", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.133773"}, {"operation": "Clean-function_discovery.py", "category": "CLEANUP", "status": "PASS", "message": "Test script removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.136027"}, {"operation": "Clean-comprehensive_function_tester.py", "category": "CLEANUP", "status": "PASS", "message": "Test script removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.138575"}, {"operation": "Clean-view_url_tester.py", "category": "CLEANUP", "status": "PASS", "message": "Test script removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.140631"}, {"operation": "Clean-api_integration_tester.py", "category": "CLEANUP", "status": "PASS", "message": "Test script removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.141987"}, {"operation": "Clean-business_workflow_tester.py", "category": "CLEANUP", "status": "PASS", "message": "Test script removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.143711"}, {"operation": "Clean-comprehensive_form_validator.py", "category": "CLEANUP", "status": "PASS", "message": "Test script removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.145377"}, {"operation": "Clean-security_permission_tester.py", "category": "CLEANUP", "status": "PASS", "message": "Test script removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.147122"}, {"operation": "Clean-performance_load_tester.py", "category": "CLEANUP", "status": "PASS", "message": "Test script removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.149412"}, {"operation": "Clean-comprehensive_testing_summary.py", "category": "CLEANUP", "status": "PASS", "message": "Test script removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.151533"}, {"operation": "Clean-function_discovery_report.json", "category": "CLEANUP", "status": "PASS", "message": "Test report removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.154076"}, {"operation": "Clean-comprehensive_test_report.json", "category": "CLEANUP", "status": "PASS", "message": "Test report removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.157670"}, {"operation": "Clean-view_url_test_report.json", "category": "CLEANUP", "status": "PASS", "message": "Test report removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.159269"}, {"operation": "Clean-api_integration_test_report.json", "category": "CLEANUP", "status": "PASS", "message": "Test report removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.160801"}, {"operation": "Clean-business_workflow_test_report.json", "category": "CLEANUP", "status": "PASS", "message": "Test report removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.162498"}, {"operation": "Clean-form_validation_test_report.json", "category": "CLEANUP", "status": "PASS", "message": "Test report removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.164443"}, {"operation": "Clean-security_permission_test_report.json", "category": "CLEANUP", "status": "PASS", "message": "Test report removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.166201"}, {"operation": "Clean-performance_load_test_report.json", "category": "CLEANUP", "status": "PASS", "message": "Test report removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.169219"}, {"operation": "Clean-final_comprehensive_test_report.json", "category": "CLEANUP", "status": "PASS", "message": "Test report removed successfully", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.170988"}, {"operation": "Django-Setup", "category": "VERIFICATION", "status": "PASS", "message": "Django configuration intact", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.172173"}, {"operation": "Model-Imports", "category": "VERIFICATION", "status": "PASS", "message": "Core models importable", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.172854"}, {"operation": "Database-Operations", "category": "VERIFICATION", "status": "PASS", "message": "Database accessible (Users: 40, Patients: 17)", "error": null, "details": null, "timestamp": "2025-08-02T09:02:51.186267"}, {"operation": "URL-Configuration", "category": "VERIFICATION", "status": "PASS", "message": "URL routing functional", "error": null, "details": null, "timestamp": "2025-08-02T09:02:53.418939"}]}