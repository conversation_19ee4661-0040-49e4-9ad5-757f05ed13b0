# Generated by Django 5.2 on 2025-06-05 16:24

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0005_alter_customuser_phone_number_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuserprofile',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='auditlog',
            name='action',
            field=models.CharField(choices=[('create', 'Create'), ('update', 'Update'), ('deactivate', 'Deactivate'), ('delete', 'Delete'), ('privilege_change', 'Privilege Change'), ('user_dashboard_view', 'User Dashboard View'), ('user_bulk_action', 'User Bulk Action')], max_length=25),
        ),
        migrations.AlterField(
            model_name='auditlog',
            name='target_user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='targeted_logs', to=settings.AUTH_USER_MODEL, verbose_name='affected user'),
        ),
        migrations.AlterField(
            model_name='department',
            name='name',
            field=models.CharField(max_length=100, unique=True),
        ),
    ]
