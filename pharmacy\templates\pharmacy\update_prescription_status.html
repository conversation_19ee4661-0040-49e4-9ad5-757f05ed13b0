{% extends "base.html" %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Update Status for Prescription #{{ prescription.id }}</h6>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                <div class="form-group">
                    <label for="status">Select New Status:</label>
                    <select name="status" id="status" class="form-control">
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if value == prescription.status %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">Update Status</button>
                <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-secondary">Cancel</a>
            </form>
        </div>
    </div>
</div>
{% endblock %}