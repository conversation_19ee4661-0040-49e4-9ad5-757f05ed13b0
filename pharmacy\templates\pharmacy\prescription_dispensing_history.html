{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Prescription Details - #{{ prescription.id }}</h6>
        </div>
        <div class="card-body">
            <p><strong>Patient:</strong> {{ prescription.patient.get_full_name }}</p>
            <p><strong>Doctor:</strong> {{ prescription.doctor.get_full_name }}</p>
            <p><strong>Prescription Date:</strong> {{ prescription.prescription_date }}</p>
            <p><strong>Status:</strong> {{ prescription.get_status_display }}</p>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Dispensing Log</h6>
        </div>
        <div class="card-body">
            {% if dispensing_logs %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Medication</th>
                            <th>Dispensed Quantity</th>
                            <th>Dispensed By</th>
                            <th>Dispensed Date</th>
                            <th>Dispensary</th>
                            <th>Unit Price</th>
                            <th>Total Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in dispensing_logs %}
                        <tr>
                            <td>{{ log.prescription_item.medication.name }} ({{ log.prescription_item.medication.strength }})</td>
                            <td>{{ log.dispensed_quantity }}</td>
                            <td>{{ log.dispensed_by.get_full_name|default:"N/A" }}</td>
                            <td>{{ log.dispensed_date|date:"Y-m-d H:i" }}</td>
                            <td>{{ log.dispensary.name|default:"N/A" }}</td>
                            <td>{{ log.unit_price_at_dispense }}</td>
                            <td>{{ log.total_price_for_this_log }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p>No dispensing history for this prescription.</p>
            {% endif %}
        </div>
    </div>

    <div class="card-footer">
        <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-secondary">Back to Prescription</a>
    </div>
</div>
{% endblock %}