# Generated by Django 5.2 on 2025-05-31 08:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('appointments', '0001_initial'),
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='appointment',
            name='patient',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='patients.patient'),
        ),
        migrations.AddField(
            model_name='appointmentfollowup',
            name='appointment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='follow_ups', to='appointments.appointment'),
        ),
        migrations.AddField(
            model_name='appointmentfollowup',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='doctorleave',
            name='doctor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='doctor_leaves', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='doctorschedule',
            name='doctor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='doctor_schedules', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='doctorschedule',
            unique_together={('doctor', 'weekday')},
        ),
    ]
