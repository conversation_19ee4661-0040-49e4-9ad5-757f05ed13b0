{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}Laboratory Payment History{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-history"></i> Payment History - {{ test_request.patient.get_full_name }}
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'test_request_detail' test_request.id %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Test Request
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Test Request Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Test Request Details</h5>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Request Date:</strong></td>
                                    <td>{{ test_request.request_date|date:"M d, Y" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Test:</strong></td>
                                    <td>{{ test_request.test.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Category:</strong></td>
                                    <td>{{ test_request.test.category.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Requested By:</strong></td>
                                    <td>{{ test_request.requested_by.get_full_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-{% if test_request.status == 'completed' %}success{% elif test_request.status == 'in_progress' %}warning{% elif test_request.status == 'payment_confirmed' %}info{% else %}secondary{% endif %}">
                                            {{ test_request.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Invoice Summary</h5>
                            {% if invoice %}
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Invoice #:</strong></td>
                                    <td>{{ invoice.invoice_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Amount:</strong></td>
                                    <td>{{ invoice.total_amount|currency }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Amount Paid:</strong></td>
                                    <td>{{ invoice.amount_paid|currency }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Balance:</strong></td>
                                    <td class="{% if invoice.balance > 0 %}text-danger{% else %}text-success{% endif %}">
                                        {{ invoice.balance|currency }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-{% if invoice.status == 'paid' %}success{% elif invoice.status == 'partial' %}warning{% else %}danger{% endif %}">
                                            {{ invoice.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                            {% else %}
                            <p class="text-muted">No invoice generated yet.</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Payment History -->
                    <h5>Payment Records</h5>
                    {% if payments %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                    <th>Source</th>
                                    <th>Transaction ID</th>
                                    <th>Recorded By</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>{{ payment.payment_date|date:"M d, Y H:i" }}</td>
                                    <td class="text-success font-weight-bold">{{ payment.amount|currency }}</td>
                                    <td>
                                        <span class="badge badge-info">{{ payment.get_payment_method_display }}</span>
                                    </td>
                                    <td>
                                        {% if payment.payment_source == 'patient_wallet' %}
                                            <span class="badge badge-primary">
                                                <i class="fas fa-wallet"></i> Wallet
                                            </span>
                                        {% else %}
                                            <span class="badge badge-secondary">
                                                <i class="fas fa-building"></i> Direct
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if payment.transaction_id %}
                                            <code>{{ payment.transaction_id }}</code>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ payment.recorded_by.get_full_name }}</td>
                                    <td>
                                        {% if payment.notes %}
                                            {{ payment.notes|truncatechars:50 }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-info">
                                    <td><strong>Total Paid:</strong></td>
                                    <td class="text-success font-weight-bold">{{ total_paid|currency }}</td>
                                    <td colspan="5"></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No payments recorded for this test request yet.
                    </div>
                    {% endif %}

                    <!-- Action Buttons -->
                    {% if invoice and invoice.balance > 0 %}
                    <div class="mt-3">
                        <a href="{% url 'laboratory_payment' test_request.id %}" class="btn btn-primary">
                            <i class="fas fa-credit-card"></i> Record Payment
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}