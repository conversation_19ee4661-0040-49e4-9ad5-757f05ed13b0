{% extends 'base.html' %}

{% block title %}Surgery Types{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>Surgery Types</h2>
        <a href="{% url 'theatre:surgery_type_create' %}" class="btn btn-primary">Add New Surgery Type</a>
    </div>
    <div class="card">
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Category</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for type in object_list %}
                    <tr>
                        <td>{{ type.name }}</td>
                        <td>{{ type.category }}</td>
                        <td>
                            <a href="{% url 'theatre:surgery_type_detail' type.pk %}" class="btn btn-info btn-sm">View</a>
                            <a href="{% url 'theatre:surgery_type_update' type.pk %}" class="btn btn-warning btn-sm">Edit</a>
                            <a href="{% url 'theatre:surgery_type_delete' type.pk %}" class="btn btn-danger btn-sm">Delete</a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="3" class="text-center">No surgery types found.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}