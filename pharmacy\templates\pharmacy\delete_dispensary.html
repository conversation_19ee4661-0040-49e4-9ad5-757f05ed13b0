{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2>Delete Dispensary: {{ dispensary.name }}</h2>
    <p>Are you sure you want to delete this dispensary? This action cannot be undone.</p>
    {% if dispensary.medications.exists %}
        <div class="alert alert-danger">
            This dispensary has associated medications. You cannot delete it until all medications are moved or deleted.
        </div>
    {% endif %}
    <form method="post">
        {% csrf_token %}
        <button type="submit" class="btn btn-danger" {% if dispensary.medications.exists %}disabled{% endif %}>Confirm Delete</button>
        <a href="{% url 'pharmacy:dispensary_list' %}" class="btn btn-secondary">Cancel</a>
    </form>
</div>
{% endblock %}