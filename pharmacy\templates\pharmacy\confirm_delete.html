{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card card-danger">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                </div>
                <div class="card-body">
                    <p>Are you sure you want to delete "<strong>{{ object }}</strong>"?</p>
                    <form method="post">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">Confirm Delete</button>
                        <a href="javascript:history.back()" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}