# Generated by Django 5.2 on 2025-06-29 17:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('patients', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='NHIAPatient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nhia_reg_number', models.CharField(help_text='Unique NHIA registration number', max_length=50, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('date_registered', models.DateTimeField(auto_now_add=True)),
                ('patient', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='nhia_info', to='patients.patient')),
            ],
            options={
                'verbose_name': 'NHIA Patient',
                'verbose_name_plural': 'NHIA Patients',
            },
        ),
    ]
