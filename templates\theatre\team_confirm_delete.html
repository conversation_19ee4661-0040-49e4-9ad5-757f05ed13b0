{% extends 'base.html' %}

{% block title %}Delete Team Member{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Confirm Delete
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <strong>Warning:</strong> This action cannot be undone.
                    </div>
                    
                    <p>Are you sure you want to delete this team member assignment?</p>
                    
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6>Team Member Details:</h6>
                            <ul class="list-unstyled mb-0">
                                <li><strong>Staff Member:</strong> {{ object.staff.get_full_name }}</li>
                                <li><strong>Role:</strong> {{ object.get_role_display }}</li>
                                <li><strong>Surgery:</strong> {{ object.surgery.surgery_type.name }} for {{ object.surgery.patient.get_full_name }}</li>
                                <li><strong>Surgery Date:</strong> {{ object.surgery.scheduled_date|date:"d/m/Y H:i" }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <form method="post">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Yes, Delete
                        </button>
                        <a href="{% url 'theatre:team_detail' object.pk %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
