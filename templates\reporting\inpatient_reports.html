{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ title }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Inpatients</h6>
        </div>
        <div class="card-body">
            <form method="get">
                <div class="row">
                    <div class="col-md-4">{{ form.start_date.label_tag }} {{ form.start_date }}</div>
                    <div class="col-md-4">{{ form.end_date.label_tag }} {{ form.end_date }}</div>
                    <div class="col-md-4">{{ form.patient.label_tag }} {{ form.patient }}</div>
                </div>
                <button type="submit" class="btn btn-primary mt-3">Filter</button>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Inpatient List</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Patient</th>
                            <th>Admission Date</th>
                            <th>Discharge Date</th>
                            <th>Ward</th>
                            <th>Bed</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for inpatient in page_obj %}
                        <tr>
                            <td>{{ inpatient.id }}</td>
                            <td>{{ inpatient.patient.get_full_name }}</td>
                            <td>{{ inpatient.admission_date }}</td>
                            <td>{{ inpatient.discharge_date|default:"N/A" }}</td>
                            <td>{{ inpatient.ward }}</td>
                            <td>{{ inpatient.bed }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">No inpatients found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% include 'pagination.html' %}
        </div>
    </div>
</div>
{% endblock %}