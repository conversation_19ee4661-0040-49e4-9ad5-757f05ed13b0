{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{% url 'pharmacy:add_dispensary' %}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> Add New Dispensary
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Dispensaries</h6>
        </div>
        <div class="card-body">
            {% if dispensaries %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Location</th>
                            <th>Manager</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for dispensary in dispensaries %}
                        <tr>
                            <td>{{ dispensary.name }}</td>
                            <td>{{ dispensary.location|default:"N/A" }}</td>
                            <td>{{ dispensary.manager.get_full_name|default:"N/A" }}</td>
                            <td>
                                {% if dispensary.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'pharmacy:edit_dispensary' dispensary.id %}" class="btn btn-info btn-sm">Edit</a>
                                <a href="{% url 'pharmacy:delete_dispensary' dispensary.id %}" class="btn btn-danger btn-sm">Delete</a>
                                <a href="{% url 'pharmacy:dispensary_inventory' dispensary.id %}" class="btn btn-primary btn-sm">Inventory</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p>No dispensaries found.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}