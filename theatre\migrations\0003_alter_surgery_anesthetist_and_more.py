# Generated by Django 5.2 on 2025-06-27 16:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('theatre', '0002_rename_notes_surgicalteam_usage_notes'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='surgery',
            name='anesthetist',
            field=models.ForeignKey(limit_choices_to={'profile__specialization__icontains': 'anesthetist'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='anesthetist_surgeries', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='surgery',
            name='primary_surgeon',
            field=models.ForeignKey(limit_choices_to={'profile__specialization__icontains': 'surgeon'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='primary_surgeries', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='PreOperativeChecklist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('patient_identified', models.BooleanField(default=False)),
                ('site_marked', models.BooleanField(default=False)),
                ('anesthesia_safety_check_completed', models.BooleanField(default=False)),
                ('surgical_safety_checklist_completed', models.BooleanField(default=False)),
                ('consent_confirmed', models.BooleanField(default=False)),
                ('allergies_reviewed', models.BooleanField(default=False)),
                ('imaging_available', models.BooleanField(default=False)),
                ('blood_products_available', models.BooleanField(default=False)),
                ('antibiotics_administered', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(auto_now_add=True)),
                ('completed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('surgery', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='pre_op_checklist', to='theatre.surgery')),
            ],
            options={
                'verbose_name': 'Pre-Operative Checklist',
                'verbose_name_plural': 'Pre-Operative Checklists',
            },
        ),
    ]
