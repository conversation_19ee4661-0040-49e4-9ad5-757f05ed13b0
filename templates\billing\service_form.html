{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">Service Name</label>
                        {{ form.name|add_class:"form-control" }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {{ form.name.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                        {{ form.category|add_class:"form-control" }}
                        {% if form.category.errors %}
                            <div class="text-danger">
                                {{ form.category.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.price.id_for_label }}" class="form-label">Price</label>
                        {{ form.price|add_class:"form-control" }}
                        {% if form.price.errors %}
                            <div class="text-danger">
                                {{ form.price.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description|add_class:"form-control" }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {{ form.description.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'billing:services' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Save Service
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
