{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:dashboard' %}">Dashboard</a></li>
<li class="breadcrumb-item"><a href="{% url 'retainership:retainership_patient_list' %}">Retainership Patients</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ title }}</li>
{% endblock breadcrumbs %}

{% block content %}
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{{ title }}</h6>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            {{ form|crispy }}
            <button type="submit" class="btn btn-primary">Register Patient</button>
        </form>
    </div>
</div>
{% endblock %}
