{% extends 'base.html' %}
{% load core_form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{% url 'consultations:consulting_room_list' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Consulting Room Details</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.room_number.id_for_label }}" class="form-label">Room Number</label>
                                {{ form.room_number|add_class:"form-control" }}
                                {% if form.room_number.errors %}
                                    <div class="text-danger">
                                        {{ form.room_number.errors }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.floor.id_for_label }}" class="form-label">Floor</label>
                                {{ form.floor|add_class:"form-control" }}
                                {% if form.floor.errors %}
                                    <div class="text-danger">
                                        {{ form.floor.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.department.id_for_label }}" class="form-label">Department</label>
                            {{ form.department|add_class:"form-control select2" }}
                            {% if form.department.errors %}
                                <div class="text-danger">
                                    {{ form.department.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            {{ form.description|add_class:"form-control" }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {{ form.description.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3 form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                Active
                            </label>
                            {% if form.is_active.errors %}
                                <div class="text-danger">
                                    {{ form.is_active.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'consultations:consulting_room_list' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Save Room
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
