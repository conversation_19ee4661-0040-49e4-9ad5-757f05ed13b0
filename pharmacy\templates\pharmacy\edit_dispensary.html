{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h2>Edit Dispensary</h2>
    <form method="post" novalidate>
        {% csrf_token %}
        <div class="card mb-3">
            <div class="card-body">
                {% for field in form %}
                    <div class="mb-3">
                        <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                        {{ field }}
                        {% if field.help_text %}
                            <div class="form-text">{{ field.help_text }}</div>
                        {% endif %}
                        {% for error in field.errors %}
                            <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endfor %}
            </div>
        </div>
        <button type="submit" class="btn btn-primary">Save Changes</button>
        <a href="{% url 'pharmacy:dispensary_list' %}" class="btn btn-secondary">Cancel</a>
    </form>
</div>
{% endblock %}
