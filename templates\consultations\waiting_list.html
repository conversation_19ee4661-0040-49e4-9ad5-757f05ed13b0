{% extends 'base.html' %}
{% load core_form_tags %}
{% load consultation_tags %}

{% block title %}Patient Waiting List - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Patient Waiting List</h1>
        <a href="{% url 'consultations:add_to_waiting_list' %}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> Add Patient to Waiting List
        </a>
    </div>

    <!-- Search and Filter Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Search & Filter</h6>
        </div>
        <div class="card-body">
            <form method="get" class="mb-0">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="Patient name or ID">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="doctor" class="form-label">Doctor</label>
                        <select class="form-control select2" id="doctor" name="doctor">
                            <option value="">All Doctors</option>
                            {% for doc in doctors %}
                                <option value="{{ doc.id }}" {% if doctor == doc.id|stringformat:"s" %}selected{% endif %}>Dr. {{ doc.get_full_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="consulting_room" class="form-label">Consulting Room</label>
                        <select class="form-control select2" id="consulting_room" name="consulting_room">
                            <option value="">All Rooms</option>
                            {% for room in consulting_rooms %}
                                <option value="{{ room.id }}" {% if consulting_room == room.id|stringformat:"s" %}selected{% endif %}>{{ room.room_number }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-control select2" id="priority" name="priority">
                            <option value="">All</option>
                            <option value="normal" {% if priority == 'normal' %}selected{% endif %}>Normal</option>
                            <option value="urgent" {% if priority == 'urgent' %}selected{% endif %}>Urgent</option>
                            <option value="emergency" {% if priority == 'emergency' %}selected{% endif %}>Emergency</option>
                        </select>
                    </div>
                    <div class="col-md-1 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search fa-sm"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Waiting List -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Current Waiting List</h6>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" id="refreshBtn">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="waitingListTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Patient</th>
                                    <th>Doctor</th>
                                    <th>Room</th>
                                    <th>Check-in Time</th>
                                    <th>Wait Time</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for entry in waiting_entries %}
                                    <tr class="{% if entry.priority == 'emergency' %}table-danger{% elif entry.priority == 'urgent' %}table-warning{% endif %}">
                                        <td>
                                            <a href="{% url 'patients:detail' entry.patient.id %}">
                                                {{ entry.patient.get_full_name }}
                                            </a>
                                            <br>
                                            <small class="text-muted">{{ entry.patient.patient_id }}</small>
                                        </td>
                                        <td>Dr. {{ entry.doctor.get_full_name }}</td>
                                        <td>{{ entry.consulting_room.room_number }}</td>
                                        <td>{{ entry.check_in_time|date:"M d, Y" }} at {{ entry.check_in_time|time:"h:i A" }}</td>
                                        <td>
                                            <span class="waiting-time" data-checkin="{{ entry.check_in_time|date:'c' }}">
                                                Calculating...
                                            </span>
                                        </td>
                                        <td>
                                            {% if entry.priority == 'normal' %}
                                                <span class="badge bg-success">Normal</span>
                                            {% elif entry.priority == 'urgent' %}
                                                <span class="badge bg-warning">Urgent</span>
                                            {% elif entry.priority == 'emergency' %}
                                                <span class="badge bg-danger">Emergency</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if entry.status == 'waiting' %}
                                                <span class="badge bg-warning">Waiting</span>
                                            {% elif entry.status == 'in_progress' %}
                                                <span class="badge bg-info">In Progress</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                {% if entry.status == 'waiting' %}
                                                    <form method="post" action="{% url 'consultations:update_waiting_status' entry.id %}">
                                                        {% csrf_token %}
                                                        <input type="hidden" name="status" value="in_progress">
                                                        <button type="submit" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-play"></i> Start
                                                        </button>
                                                    </form>
                                                {% endif %}

                                                {% if entry.status == 'in_progress' %}
                                                    <form method="post" action="{% url 'consultations:update_waiting_status' entry.id %}">
                                                        {% csrf_token %}
                                                        <input type="hidden" name="status" value="completed">
                                                        <button type="submit" class="btn btn-sm btn-success">
                                                            <i class="fas fa-check"></i> Complete
                                                        </button>
                                                    </form>
                                                {% endif %}

                                                <form method="post" action="{% url 'consultations:update_waiting_status' entry.id %}">
                                                    {% csrf_token %}
                                                    <input type="hidden" name="status" value="cancelled">
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-times"></i> Cancel
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                {% empty %}
                                    <tr>
                                        <td colspan="8" class="text-center">No patients in the waiting list.</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize select2 for better dropdown experience
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });

        // Function to calculate and update waiting times
        function updateWaitingTimes() {
            $('.waiting-time').each(function() {
                var checkinTime = new Date($(this).data('checkin'));
                var now = new Date();
                var diffMs = now - checkinTime;
                var diffMins = Math.floor(diffMs / 60000);
                var diffHrs = Math.floor(diffMins / 60);
                diffMins = diffMins % 60;

                var waitTimeText = '';
                if (diffHrs > 0) {
                    waitTimeText = diffHrs + 'h ' + diffMins + 'm';
                    // Add warning class if waiting more than 1 hour
                    if (diffHrs >= 1) {
                        $(this).addClass('text-danger fw-bold');
                    }
                } else {
                    waitTimeText = diffMins + ' minutes';
                    // Add warning class if waiting more than 30 minutes
                    if (diffMins >= 30) {
                        $(this).addClass('text-warning fw-bold');
                    }
                }

                $(this).text(waitTimeText);
            });
        }

        // Initial update
        updateWaitingTimes();

        // Update every minute
        setInterval(updateWaitingTimes, 60000);

        // Refresh button
        $('#refreshBtn').click(function() {
            location.reload();
        });
    });
</script>
{% endblock %}
