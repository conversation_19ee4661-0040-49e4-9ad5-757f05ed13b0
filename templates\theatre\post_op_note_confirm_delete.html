{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Post-Operative Note{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Delete Post-Operative Note</h1>
        <a href="{% url 'theatre:surgery_detail' surgery.id %}" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Surgery
        </a>
    </div>

    <!-- Delete Confirmation Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Confirm Deletion</h6>
        </div>
        <div class="card-body">
            <p>Are you sure you want to delete this post-operative note for {{ surgery.patient }} - {{ surgery.surgery_type }}?</p>
            <p><strong>Created by:</strong> {{ object.created_by }}</p>
            <p><strong>Created at:</strong> {{ object.created_at|date:"d/m/Y H:i" }}</p>
            <p><strong>Notes:</strong> {{ object.notes|truncatewords:20 }}</p>
            
            <form method="post">
                {% csrf_token %}
                <div class="mt-4">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash mr-2"></i> Delete Note
                    </button>
                    <a href="{% url 'theatre:surgery_detail' surgery.id %}" class="btn btn-secondary ml-2">
                        <i class="fas fa-times mr-2"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}