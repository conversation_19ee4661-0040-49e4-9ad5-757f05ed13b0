{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block extra_css %}
<style>
.autocomplete-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.autocomplete-results .list-group-item {
    border-radius: 0;
    border-left: none;
    border-right: none;
}

.autocomplete-results .list-group-item:hover {
    background-color: #f8f9fa;
}

.position-relative {
    position: relative;
}
</style>
{% endblock %}

{% block title %}
{% if form.instance.pk %}
Edit Surgery
{% else %}
Create Surgery
{% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">{% if form.instance.pk %}Edit Surgery{% else %}Create Surgery{% endif %}</h2>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_patient_search">Search Patient</label>
                            <div class="position-relative">
                                {{ form.patient_search|as_crispy_field }}
                                <div id="patient-search-results" class="autocomplete-results" style="display: none;"></div>
                            </div>
                            <div id="selected-patient-info" class="mt-2" style="display: none;">
                                <div class="alert alert-info">
                                    <strong>Selected Patient:</strong>
                                    <span id="patient-name"></span> (<span id="patient-id"></span>)
                                    <br><small>Age: <span id="patient-age"></span> | Gender: <span id="patient-gender"></span> | Phone: <span id="patient-phone"></span></small>
                                </div>
                            </div>
                        </div>
                        {{ form.patient }}
                    </div>
                    <div class="col-md-6">
                        {{ form.surgery_type|as_crispy_field }}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        {{ form.primary_surgeon|as_crispy_field }}
                    </div>
                    <div class="col-md-6">
                        {{ form.anesthetist|as_crispy_field }}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        {{ form.theatre|as_crispy_field }}
                    </div>
                    <div class="col-md-6">
                        {{ form.status|as_crispy_field }}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        {{ form.scheduled_date|as_crispy_field }}
                    </div>
                    <div class="col-md-6">
                        {{ form.expected_duration|as_crispy_field }}
                    </div>
                </div>
                {{ form.pre_surgery_notes|as_crispy_field }}

                <hr>

                <h3>Surgical Team</h3>
                {{ team_formset.management_form }}
                <div id="team-form-list">
                    {% for form in team_formset %}
                        <div class="team-form">
                            <div class="row">
                                <div class="col-md-5">{{ form.staff|as_crispy_field }}</div>
                                <div class="col-md-5">{{ form.role|as_crispy_field }}</div>
                                <div class="col-md-2 d-flex align-items-center">
                                    {% if form.instance.pk %}
                                        {{ form.DELETE }}
                                    {% endif %}
                                </div>
                            </div>
                            {{ form.usage_notes|as_crispy_field }}
                        </div>
                    {% endfor %}
                </div>
                <button type="button" id="add-team-form" class="btn btn-info btn-sm">Add Team Member</button>
                <div id="empty-team-form" style="display:none;">
                    <div class="team-form">
                        <div class="row">
                            <div class="col-md-5">{{ team_formset.empty_form.staff|as_crispy_field }}</div>
                            <div class="col-md-5">{{ team_formset.empty_form.role|as_crispy_field }}</div>
                        </div>
                        {{ team_formset.empty_form.usage_notes|as_crispy_field }}
                    </div>
                </div>

                <hr>

                <h3>Surgical Equipment</h3>
                {{ equipment_formset.management_form }}
                <div id="equipment-form-list">
                    {% for form in equipment_formset %}
                        <div class="equipment-form">
                            <div class="row">
                                <div class="col-md-5">{{ form.equipment|as_crispy_field }}</div>
                                <div class="col-md-5">{{ form.quantity_used|as_crispy_field }}</div>
                                <div class="col-md-2 d-flex align-items-center">
                                    {% if form.instance.pk %}
                                        {{ form.DELETE }}
                                    {% endif %}
                                </div>
                            </div>
                            {{ form.notes|as_crispy_field }}
                        </div>
                    {% endfor %}
                </div>
                <button type="button" id="add-equipment-form" class="btn btn-info btn-sm">Add Equipment</button>
                <div id="empty-equipment-form" style="display:none;">
                    <div class="equipment-form">
                        <div class="row">
                            <div class="col-md-5">{{ equipment_formset.empty_form.equipment|as_crispy_field }}</div>
                            <div class="col-md-5">{{ equipment_formset.empty_form.quantity_used|as_crispy_field }}</div>
                        </div>
                        {{ equipment_formset.empty_form.notes|as_crispy_field }}
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">Save</button>
                    <a href="{% url 'theatre:surgery_list' %}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Patient search functionality
    const patientSearchField = document.getElementById('id_patient_search');
    const patientIdField = document.getElementById('id_patient');
    const searchResults = document.getElementById('patient-search-results');
    const selectedPatientInfo = document.getElementById('selected-patient-info');
    let searchTimeout;

    if (patientSearchField) {
        patientSearchField.addEventListener('input', function() {
            const searchTerm = this.value.trim();

            // Clear previous timeout
            clearTimeout(searchTimeout);

            if (searchTerm.length < 3) {
                searchResults.style.display = 'none';
                return;
            }

            // Debounce search
            searchTimeout = setTimeout(() => {
                fetch(`/patients/search/?term=${encodeURIComponent(searchTerm)}`)
                    .then(response => response.json())
                    .then(data => {
                        displaySearchResults(data);
                    })
                    .catch(error => {
                        console.error('Error searching patients:', error);
                        searchResults.style.display = 'none';
                    });
            }, 300);
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!patientSearchField.contains(e.target) && !searchResults.contains(e.target)) {
                searchResults.style.display = 'none';
            }
        });
    }

    function displaySearchResults(patients) {
        if (patients.length === 0) {
            searchResults.style.display = 'none';
            return;
        }

        let html = '<div class="list-group">';
        patients.forEach(patient => {
            html += `
                <a href="#" class="list-group-item list-group-item-action patient-result"
                   data-patient-id="${patient.id}"
                   data-patient-name="${patient.name}"
                   data-patient-pid="${patient.patient_id}"
                   data-patient-age="${patient.age}"
                   data-patient-gender="${patient.gender}"
                   data-patient-phone="${patient.phone}">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${patient.name}</h6>
                        <small>${patient.patient_id}</small>
                    </div>
                    <p class="mb-1">Age: ${patient.age} | Gender: ${patient.gender}</p>
                    <small>Phone: ${patient.phone}</small>
                </a>
            `;
        });
        html += '</div>';

        searchResults.innerHTML = html;
        searchResults.style.display = 'block';

        // Add click handlers to results
        searchResults.querySelectorAll('.patient-result').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                selectPatient(this);
            });
        });
    }

    function selectPatient(element) {
        const patientId = element.dataset.patientId;
        const patientName = element.dataset.patientName;
        const patientPid = element.dataset.patientPid;
        const patientAge = element.dataset.patientAge;
        const patientGender = element.dataset.patientGender;
        const patientPhone = element.dataset.patientPhone;

        // Set the hidden patient field
        patientIdField.value = patientId;

        // Update search field with patient name
        patientSearchField.value = patientName;

        // Show selected patient info
        document.getElementById('patient-name').textContent = patientName;
        document.getElementById('patient-id').textContent = patientPid;
        document.getElementById('patient-age').textContent = patientAge;
        document.getElementById('patient-gender').textContent = patientGender;
        document.getElementById('patient-phone').textContent = patientPhone;

        selectedPatientInfo.style.display = 'block';
        searchResults.style.display = 'none';
    }
    
    // Team Formset
    const addTeamFormBtn = document.getElementById('add-team-form');
    const teamFormList = document.getElementById('team-form-list');
    const emptyTeamForm = document.getElementById('empty-team-form').innerHTML;
    const teamTotalForms = document.querySelector('input[name="team_members-TOTAL_FORMS"]');

    if (addTeamFormBtn && teamFormList && teamTotalForms) {
        addTeamFormBtn.addEventListener('click', function() {
            let formNum = parseInt(teamTotalForms.value);
            let newForm = emptyTeamForm.replace(/__prefix__/g, formNum);
            teamFormList.insertAdjacentHTML('beforeend', newForm);
            teamTotalForms.value = formNum + 1;
        });
    }

    // Equipment Formset
    const addEquipmentFormBtn = document.getElementById('add-equipment-form');
    const equipmentFormList = document.getElementById('equipment-form-list');
    const emptyEquipmentForm = document.getElementById('empty-equipment-form').innerHTML;
    const equipmentTotalForms = document.querySelector('input[name="equipment_used-TOTAL_FORMS"]');

    if (addEquipmentFormBtn && equipmentFormList && equipmentTotalForms) {
        addEquipmentFormBtn.addEventListener('click', function() {
            let formNum = parseInt(equipmentTotalForms.value);
            let newForm = emptyEquipmentForm.replace(/__prefix__/g, formNum);
            equipmentFormList.insertAdjacentHTML('beforeend', newForm);
            equipmentTotalForms.value = formNum + 1;
        });
    }
});
</script>
{% endblock %}