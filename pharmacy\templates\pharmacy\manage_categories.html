{% extends "base.html" %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Add New Category</h6>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                {{ form|crispy }}
                <button type="submit" class="btn btn-primary">Add Category</button>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4 mt-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Existing Categories</h6>
        </div>
        <div class="card-body">
            {% if categories %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories %}
                        <tr>
                            <td>{{ category.name }}</td>
                            <td>{{ category.description|default:"N/A" }}</td>
                            <td>{{ category.created_at|date:"Y-m-d H:i" }}</td>
                            <td>
                                <a href="{% url 'pharmacy:edit_category' category.id %}" class="btn btn-info btn-sm">Edit</a>
                                <a href="{% url 'pharmacy:delete_category' category.id %}" class="btn btn-danger btn-sm">Delete</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p>No medication categories found.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}