{% extends 'base.html' %}

{% block title %}Doctor Schedule - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Dr. {{ doctor.get_full_name }}'s Schedule</h4>
                <a href="{% url 'appointments:create' %}" class="btn btn-light">
                    <i class="fas fa-plus"></i> Schedule Appointment
                </a>
            </div>
            <div class="card-body">
                <!-- Date Navigation -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="d-flex">
                            <a href="?date={{ prev_date|date:'Y-m-d' }}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-chevron-left"></i> Previous Day
                            </a>
                            <a href="?date={{ next_date|date:'Y-m-d' }}" class="btn btn-outline-primary">
                                Next Day <i class="fas fa-chevron-right"></i>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <h3>{{ selected_date|date:"F d, Y" }}</h3>
                        {% if selected_date == today %}
                            <span class="badge bg-info">Today</span>
                        {% endif %}
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="?date={{ today|date:'Y-m-d' }}" class="btn btn-outline-secondary">
                            <i class="fas fa-calendar-day"></i> Today
                        </a>
                    </div>
                </div>
                
                <!-- Doctor Availability Info -->
                <div class="card mb-4">
                    <div class="card-body">
                        {% if is_on_leave %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Dr. {{ doctor.get_full_name }} is on leave on this date.
                            </div>
                        {% elif schedule %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Dr. {{ doctor.get_full_name }} is available from 
                                {{ schedule.start_time|time:"h:i A" }} to {{ schedule.end_time|time:"h:i A" }} 
                                on {{ selected_date|date:"l" }}.
                            </div>
                        {% else %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                No schedule information available for Dr. {{ doctor.get_full_name }} on this date.
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Appointments Timeline -->
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Appointments for {{ selected_date|date:"F d, Y" }}</h5>
                    </div>
                    <div class="card-body">
                        {% if appointments %}
                            <div class="timeline">
                                {% for appointment in appointments %}
                                    <div class="row mb-3 pb-3 border-bottom">
                                        <div class="col-md-2">
                                            <div class="fw-bold">{{ appointment.appointment_time|time:"h:i A" }}</div>
                                            {% if appointment.end_time %}
                                                <div class="text-muted">to {{ appointment.end_time|time:"h:i A" }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-7">
                                            <div class="fw-bold">
                                                <a href="{% url 'patients:detail' appointment.patient.id %}">
                                                    {{ appointment.patient.get_full_name }}
                                                </a>
                                            </div>
                                            <div>{{ appointment.reason }}</div>
                                            <div class="text-muted">Patient ID: {{ appointment.patient.patient_id }}</div>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <div>
                                                {% if appointment.status == 'scheduled' %}
                                                    <span class="badge bg-secondary">Scheduled</span>
                                                {% elif appointment.status == 'confirmed' %}
                                                    <span class="badge bg-primary">Confirmed</span>
                                                {% elif appointment.status == 'completed' %}
                                                    <span class="badge bg-success">Completed</span>
                                                {% elif appointment.status == 'cancelled' %}
                                                    <span class="badge bg-danger">Cancelled</span>
                                                {% elif appointment.status == 'no_show' %}
                                                    <span class="badge bg-warning">No Show</span>
                                                {% endif %}
                                            </div>
                                            <div class="mt-2">
                                                {% if appointment.priority == 'normal' %}
                                                    <span class="badge bg-secondary">Normal</span>
                                                {% elif appointment.priority == 'urgent' %}
                                                    <span class="badge bg-warning">Urgent</span>
                                                {% elif appointment.priority == 'emergency' %}
                                                    <span class="badge bg-danger">Emergency</span>
                                                {% endif %}
                                            </div>
                                            <div class="mt-2">
                                                <a href="{% url 'appointments:detail' appointment.id %}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No appointments scheduled for Dr. {{ doctor.get_full_name }} on {{ selected_date|date:"F d, Y" }}.
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{% url 'appointments:list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Appointments
                    </a>
                    <a href="{% url 'appointments:manage_doctor_schedule' doctor_id=doctor.id %}" class="btn btn-primary">
                        <i class="fas fa-calendar-alt"></i> Manage Schedule
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
