{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-12 mb-3">
                            <h5 class="border-bottom pb-2">Basic Information</h5>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">Medication Name</label>
                            {{ form.name|add_class:"form-control" }}
                            {% if form.name.errors %}
                                <div class="text-danger">
                                    {{ form.name.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.generic_name.id_for_label }}" class="form-label">Generic Name</label>
                            {{ form.generic_name|add_class:"form-control" }}
                            {% if form.generic_name.errors %}
                                <div class="text-danger">
                                    {{ form.generic_name.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                            {{ form.category|add_class:"form-select select2" }}
                            {% if form.category.errors %}
                                <div class="text-danger">
                                    {{ form.category.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.dosage_form.id_for_label }}" class="form-label">Dosage Form</label>
                            {{ form.dosage_form|add_class:"form-control" }}
                            {% if form.dosage_form.errors %}
                                <div class="text-danger">
                                    {{ form.dosage_form.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.strength.id_for_label }}" class="form-label">Strength</label>
                            {{ form.strength|add_class:"form-control" }}
                            {% if form.strength.errors %}
                                <div class="text-danger">
                                    {{ form.strength.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            {{ form.description|add_class:"form-control" }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {{ form.description.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Inventory Information -->
                        <div class="col-md-12 mb-3">
                            <h5 class="border-bottom pb-2">Inventory Information</h5>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="{{ form.manufacturer.id_for_label }}" class="form-label">Manufacturer</label>
                            {{ form.manufacturer|add_class:"form-control" }}
                            {% if form.manufacturer.errors %}
                                <div class="text-danger">
                                    {{ form.manufacturer.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="{{ form.price.id_for_label }}" class="form-label">Price (₦)</label>
                            {{ form.price|add_class:"form-control" }}
                            {% if form.price.errors %}
                                <div class="text-danger">
                                    {{ form.price.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <label for="{{ form.stock_quantity.id_for_label }}" class="form-label">Stock Quantity</label>
                            {{ form.stock_quantity|add_class:"form-control" }}
                            {% if form.stock_quantity.errors %}
                                <div class="text-danger">
                                    {{ form.stock_quantity.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <label for="{{ form.reorder_level.id_for_label }}" class="form-label">Reorder Level</label>
                            {{ form.reorder_level|add_class:"form-control" }}
                            {% if form.reorder_level.errors %}
                                <div class="text-danger">
                                    {{ form.reorder_level.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <label for="{{ form.expiry_date.id_for_label }}" class="form-label">Expiry Date</label>
                            {{ form.expiry_date|add_class:"form-control" }}
                            {% if form.expiry_date.errors %}
                                <div class="text-danger">
                                    {{ form.expiry_date.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Additional Information -->
                        <div class="col-md-12 mb-3">
                            <h5 class="border-bottom pb-2">Additional Information</h5>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.side_effects.id_for_label }}" class="form-label">Side Effects</label>
                            {{ form.side_effects|add_class:"form-control" }}
                            {% if form.side_effects.errors %}
                                <div class="text-danger">
                                    {{ form.side_effects.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.precautions.id_for_label }}" class="form-label">Precautions</label>
                            {{ form.precautions|add_class:"form-control" }}
                            {% if form.precautions.errors %}
                                <div class="text-danger">
                                    {{ form.precautions.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.storage_instructions.id_for_label }}" class="form-label">Storage Instructions</label>
                            {{ form.storage_instructions|add_class:"form-control" }}
                            {% if form.storage_instructions.errors %}
                                <div class="text-danger">
                                    {{ form.storage_instructions.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <div class="form-check form-switch">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                                <div class="text-danger">
                                    {{ form.is_active.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'pharmacy:inventory' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Inventory
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Medication
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for category dropdown
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
