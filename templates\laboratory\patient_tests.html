{% extends 'base.html' %}

{% block title %}Patient Tests - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Laboratory Tests for {{ patient.get_full_name }}</h4>
                <div>
                    <a href="{% url 'laboratory:create_test_request' %}?patient_id={{ patient.id }}" class="btn btn-light me-2">
                        <i class="fas fa-plus"></i> New Test Request
                    </a>
                    <a href="{% url 'patients:detail' patient.id %}" class="btn btn-light">
                        <i class="fas fa-user"></i> Patient Profile
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Patient Information -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Patient Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="row mb-3">
                                    <div class="col-md-4 text-muted">Patient ID:</div>
                                    <div class="col-md-8">{{ patient.patient_id }}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 text-muted">Name:</div>
                                    <div class="col-md-8">{{ patient.get_full_name }}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 text-muted">Gender:</div>
                                    <div class="col-md-8">{{ patient.get_gender_display }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="row mb-3">
                                    <div class="col-md-4 text-muted">Date of Birth:</div>
                                    <div class="col-md-8">{{ patient.date_of_birth|date:"F d, Y" }}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 text-muted">Age:</div>
                                    <div class="col-md-8">{{ patient.age }} years</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 text-muted">Phone:</div>
                                    <div class="col-md-8">{{ patient.phone_number }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Test Requests -->
                <h5 class="border-bottom pb-2 mb-3">Test Requests</h5>
                {% if test_requests %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Date</th>
                                    <th>Doctor</th>
                                    <th>Tests</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in test_requests %}
                                    <tr class="{% if request.status == 'pending' %}table-warning{% elif request.status == 'collected' %}table-info{% elif request.status == 'processing' %}table-secondary{% elif request.status == 'completed' %}table-success{% elif request.status == 'cancelled' %}table-danger{% endif %}">
                                        <td>{{ request.id }}</td>
                                        <td>{{ request.request_date|date:"M d, Y" }}</td>
                                        <td>Dr. {{ request.doctor.get_full_name }}</td>
                                        <td>
                                            {% for test in request.tests.all %}
                                                <span class="badge bg-primary">{{ test.name }}</span>
                                            {% endfor %}
                                        </td>
                                        <td>
                                            {% if request.priority == 'normal' %}
                                                <span class="badge bg-success">Normal</span>
                                            {% elif request.priority == 'urgent' %}
                                                <span class="badge bg-warning">Urgent</span>
                                            {% elif request.priority == 'emergency' %}
                                                <span class="badge bg-danger">Emergency</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if request.status == 'pending' %}
                                                <span class="badge bg-warning">Pending</span>
                                            {% elif request.status == 'collected' %}
                                                <span class="badge bg-info">Sample Collected</span>
                                            {% elif request.status == 'processing' %}
                                                <span class="badge bg-secondary">Processing</span>
                                            {% elif request.status == 'completed' %}
                                                <span class="badge bg-success">Completed</span>
                                            {% elif request.status == 'cancelled' %}
                                                <span class="badge bg-danger">Cancelled</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{% url 'laboratory:test_request_detail' request.id %}" class="btn btn-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if request.status != 'completed' and request.status != 'cancelled' %}
                                                    <a href="{% url 'laboratory:create_test_result' request.id %}" class="btn btn-success" title="Add Result">
                                                        <i class="fas fa-vial"></i>
                                                    </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No test requests found for this patient.
                    </div>
                {% endif %}
                
                <!-- Test Results -->
                <h5 class="border-bottom pb-2 mb-3 mt-4">Recent Test Results</h5>
                {% with results=patient.test_requests.all|dictsort:"-request_date"|slice:":10" %}
                    {% if results %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Test</th>
                                        <th>Doctor</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for request in results %}
                                        {% for result in request.results.all %}
                                            <tr>
                                                <td>{{ result.result_date|date:"M d, Y" }}</td>
                                                <td>{{ result.test.name }}</td>
                                                <td>Dr. {{ request.doctor.get_full_name }}</td>
                                                <td>
                                                    {% if result.verified_by %}
                                                        <span class="badge bg-success">Verified</span>
                                                    {% else %}
                                                        <span class="badge bg-warning">Not Verified</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="{% url 'laboratory:result_detail' result.id %}" class="btn btn-info" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{% url 'laboratory:print_result' result.id %}" class="btn btn-secondary" title="Print" target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No test results found for this patient.
                        </div>
                    {% endif %}
                {% endwith %}
            </div>
            <div class="card-footer">
                <a href="{% url 'patients:detail' patient.id %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Patient
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
