{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        border-left: 4px solid;
        transition: transform 0.2s;
    }
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .stats-card.primary { border-left-color: #4e73df; }
    .stats-card.success { border-left-color: #1cc88a; }
    .stats-card.info { border-left-color: #36b9cc; }
    .stats-card.warning { border-left-color: #f6c23e; }
    .stats-card.danger { border-left-color: #e74a3b; }
    
    .filter-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            <button onclick="window.print()" class="btn btn-primary btn-sm">
                <i class="fas fa-print"></i> Print Report
            </button>
            <button onclick="exportToCSV()" class="btn btn-success btn-sm">
                <i class="fas fa-file-csv"></i> Export CSV
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="card filter-card shadow mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-2">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-2">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-2">
                    <label for="surgery_type" class="form-label">Surgery Type</label>
                    <select class="form-control" id="surgery_type" name="surgery_type">
                        <option value="">All Surgery Types</option>
                        {% for surgery_type in surgery_types %}
                        <option value="{{ surgery_type.id }}" {% if surgery_type.id|stringformat:"s" == selected_surgery_type %}selected{% endif %}>
                            {{ surgery_type.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="theatre" class="form-label">Theatre</label>
                    <select class="form-control" id="theatre" name="theatre">
                        <option value="">All Theatres</option>
                        {% for theatre in theatres %}
                        <option value="{{ theatre.id }}" {% if theatre.id|stringformat:"s" == selected_theatre %}selected{% endif %}>
                            {{ theatre.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="scheduled" {% if selected_status == 'scheduled' %}selected{% endif %}>Scheduled</option>
                        <option value="in_progress" {% if selected_status == 'in_progress' %}selected{% endif %}>In Progress</option>
                        <option value="completed" {% if selected_status == 'completed' %}selected{% endif %}>Completed</option>
                        <option value="cancelled" {% if selected_status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                        <option value="postponed" {% if selected_status == 'postponed' %}selected{% endif %}>Postponed</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-light w-100">
                        <i class="fas fa-filter"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Overall Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Surgeries</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ overall_stats.total_surgeries|default:"0" }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-procedures fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Success Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ success_rate|floatformat:1 }}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Cancellation Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ cancellation_rate|floatformat:1 }}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Complications</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ complications_count|default:"0" }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Theatre Performance -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Performance by Theatre</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Theatre</th>
                                    <th>Total Surgeries</th>
                                    <th>Completed</th>
                                    <th>Cancelled</th>
                                    <th>Unique Patients</th>
                                    <th>Utilization</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in theatre_stats %}
                                <tr>
                                    <td><strong>{{ stat.theatre__name|default:"Unknown" }}</strong></td>
                                    <td>{{ stat.total_surgeries|default:"0" }}</td>
                                    <td><span class="text-success">{{ stat.completed_surgeries|default:"0" }}</span></td>
                                    <td><span class="text-danger">{{ stat.cancelled_surgeries|default:"0" }}</span></td>
                                    <td>{{ stat.unique_patients|default:"0" }}</td>
                                    <td>
                                        {% if stat.total_surgeries > 0 %}
                                        {% widthratio stat.completed_surgeries stat.total_surgeries 100 %}%
                                        {% else %}
                                        0%
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted">No surgery data found for the selected period</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Status Distribution</h6>
                </div>
                <div class="card-body">
                    {% for status in status_stats %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-capitalize">{{ status.status }}</span>
                            <span class="font-weight-bold">{{ status.count }}</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar 
                                {% if status.status == 'completed' %}bg-success
                                {% elif status.status == 'cancelled' %}bg-danger
                                {% elif status.status == 'in_progress' %}bg-warning
                                {% else %}bg-info{% endif %}" 
                                style="width: {% widthratio status.count overall_stats.total_surgeries 100 %}%">
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Top Surgery Types and Surgeons -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Surgery Types by Volume</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Surgery Type</th>
                                    <th>Total</th>
                                    <th>Completed</th>
                                    <th>Success Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for surgery_type in top_surgery_types %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ surgery_type.surgery_type__name }}</td>
                                    <td>{{ surgery_type.total_surgeries }}</td>
                                    <td>{{ surgery_type.completed_surgeries }}</td>
                                    <td>
                                        {% if surgery_type.total_surgeries > 0 %}
                                        {% widthratio surgery_type.completed_surgeries surgery_type.total_surgeries 100 %}%
                                        {% else %}
                                        0%
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No data</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Surgeons by Volume</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Surgeon</th>
                                    <th>Total</th>
                                    <th>Completed</th>
                                    <th>Success Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for surgeon in top_surgeons %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ surgeon.primary_surgeon__first_name }} {{ surgeon.primary_surgeon__last_name }}</td>
                                    <td>{{ surgeon.total_surgeries }}</td>
                                    <td>{{ surgeon.completed_surgeries }}</td>
                                    <td>
                                        {% if surgeon.total_surgeries > 0 %}
                                        {% widthratio surgeon.completed_surgeries surgeon.total_surgeries 100 %}%
                                        {% else %}
                                        0%
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No data</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-primary">{{ overall_stats.unique_patients|default:"0" }}</h4>
                            <small class="text-muted">Unique Patients</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info">{{ overall_stats.unique_surgeons|default:"0" }}</h4>
                            <small class="text-muted">Active Surgeons</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success">{{ overall_stats.unique_theatres|default:"0" }}</h4>
                            <small class="text-muted">Active Theatres</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning">{{ overall_stats.avg_duration|floatformat:1|default:"0" }}h</h4>
                            <small class="text-muted">Avg Surgery Duration</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportToCSV() {
    alert('CSV export functionality will be implemented');
}
</script>
{% endblock %}
