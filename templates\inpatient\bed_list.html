{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ title }}</h4>
                <a href="{% url 'inpatient:add_bed' %}" class="btn btn-light">
                    <i class="fas fa-plus-circle me-1"></i> Add Bed
                </a>
            </div>
            <div class="card-body">
                <!-- Search and Filter Form -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Search & Filter</h5>
                    </div>
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="Search by bed number or description">
                            </div>
                            <div class="col-md-3">
                                <label for="ward" class="form-label">Ward</label>
                                <select class="form-select" id="ward" name="ward">
                                    <option value="">All Wards</option>
                                    {% for ward in wards %}
                                        <option value="{{ ward.id }}" {% if ward_id == ward.id|stringformat:"s" %}selected{% endif %}>{{ ward.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="occupancy" class="form-label">Occupancy</label>
                                <select class="form-select" id="occupancy" name="occupancy">
                                    <option value="">All</option>
                                    <option value="available" {% if occupancy == 'available' %}selected{% endif %}>Available</option>
                                    <option value="occupied" {% if occupancy == 'occupied' %}selected{% endif %}>Occupied</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="is_active" class="form-label">Status</label>
                                <select class="form-select" id="is_active" name="is_active">
                                    <option value="">All</option>
                                    <option value="true" {% if is_active == True %}selected{% endif %}>Active</option>
                                    <option value="false" {% if is_active == False %}selected{% endif %}>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i> Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Beds Table -->
                {% if page_obj %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Bed Number</th>
                                    <th>Ward</th>
                                    <th>Status</th>
                                    <th>Description</th>
                                    <th>Current Patient</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for bed in page_obj %}
                                    <tr>
                                        <td>{{ bed.bed_number }}</td>
                                        <td>
                                            <a href="{% url 'inpatient:ward_detail' bed.ward.id %}">
                                                {{ bed.ward.name }}
                                            </a>
                                        </td>
                                        <td>
                                            {% if not bed.is_active %}
                                                <span class="badge bg-secondary">Inactive</span>
                                            {% elif bed.is_occupied %}
                                                <span class="badge bg-danger">Occupied</span>
                                            {% else %}
                                                <span class="badge bg-success">Available</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ bed.description|default:"No description" }}</td>
                                        <td>
                                            {% if bed.is_occupied %}
                                                {% if bed.current_admission %}
                                                    <a href="{% url 'inpatient:admission_detail' bed.current_admission.id %}">
                                                        {{ bed.current_admission.patient.get_full_name }}
                                                    </a>
                                                {% else %}
                                                    <span class="text-muted">Unknown</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">None</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{% url 'inpatient:edit_bed' bed.id %}" class="btn btn-sm btn-secondary">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                                {% if not bed.is_occupied %}
                                                    <a href="{% url 'inpatient:delete_bed' bed.id %}" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </a>
                                                {% endif %}
                                                {% if not bed.is_occupied and bed.is_active %}
                                                    <a href="{% url 'inpatient:create_admission' %}?bed_id={{ bed.id }}" class="btn btn-sm btn-success">
                                                        <i class="fas fa-procedures"></i> Admit
                                                    </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No beds found matching your criteria.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
