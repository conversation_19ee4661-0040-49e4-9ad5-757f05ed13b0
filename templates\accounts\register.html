{% extends 'base.html' %}
{% load static %}
{% load form_tags %}

{% block title %}Register - Hospital Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card auth-form">
            <div class="card-body">
                <h2 class="card-title text-center mb-4">Register</h2>
                <form method="post">
                    {% csrf_token %}
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <ul>
                                {% for field in form %}
                                    {% for error in field.errors %}
                                        <li>{{ field.label }}: {{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                                {% for error in form.non_field_errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}
                    <!-- Hidden username field, will be set by JS -->
                    <input type="hidden" name="username" id="id_username" value="{{ form.username.value|default:'' }}">
                    <div class="mb-3">
                        <label for="id_phone_number" class="form-label">Phone Number</label>
                        {{ form.phone_number|add_class:'form-control' }}
                        {% if form.phone_number.errors %}
                            <div class="text-danger">{{ form.phone_number.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="id_first_name" class="form-label">First Name</label>
                        {{ form.first_name|add_class:'form-control' }}
                        {% if form.first_name.errors %}
                            <div class="text-danger">{{ form.first_name.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="id_last_name" class="form-label">Last Name</label>
                        {{ form.last_name|add_class:'form-control' }}
                        {% if form.last_name.errors %}
                            <div class="text-danger">{{ form.last_name.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="id_email" class="form-label">Email</label>
                        {{ form.email|add_class:'form-control' }}
                        {% if form.email.errors %}
                            <div class="text-danger">{{ form.email.errors }}</div>
                        {% endif %}
                    </div>
                    <!-- Module selection field -->
                    <div class="mb-3">
                        <label for="id_module" class="form-label">Module</label>
                        {{ form.module|add_class:'form-select' }}
                        <div class="form-text">Select the module this user will be registered for. This determines their privileges.</div>
                        {% if form.module.errors %}
                            <div class="text-danger">{{ form.module.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="id_password1" class="form-label">Password</label>
                        {{ form.password1|add_class:'form-control' }}
                        {% if form.password1.errors %}
                            <div class="text-danger">{{ form.password1.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="id_password2" class="form-label">Confirm Password</label>
                        {{ form.password2|add_class:'form-control' }}
                        {% if form.password2.errors %}
                            <div class="text-danger">{{ form.password2.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Register</button>
                    </div>
                </form>
                <div class="text-center mt-3">
                    <a href="{% url 'accounts:login' %}">Already have an account? Login</a>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
// Prefill username with phone number on input
const phoneInput = document.getElementById('id_phone_number');
const usernameInput = document.getElementById('id_username');
if (phoneInput && usernameInput) {
    phoneInput.addEventListener('input', function() {
        usernameInput.value = phoneInput.value;
    });
}
</script>
{% endblock %}
