{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}

                    <div class="mb-3">
                        <label for="{{ form.patient.id_for_label }}" class="form-label">Patient</label>
                        {{ form.patient|add_class:"form-control select2" }}
                        {% if form.patient.errors %}
                            <div class="text-danger">
                                {{ form.patient.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                        {{ form.notes|add_class:"form-control" }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {{ form.notes.errors }}
                            </div>
                        {% endif %}
                        <div class="form-text">Brief description of what this invoice is for.</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.due_date.id_for_label }}" class="form-label">Due Date</label>
                            {{ form.due_date|add_class:"form-control datepicker" }}
                            {% if form.due_date.errors %}
                                <div class="text-danger">
                                    {{ form.due_date.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                            {{ form.status|add_class:"form-control" }}
                            {% if form.status.errors %}
                                <div class="text-danger">
                                    {{ form.status.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        {% if invoice %}
                            <a href="{% url 'billing:detail' invoice.id %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                        {% else %}
                            <a href="{% url 'billing:list' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                        {% endif %}
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Save Invoice
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
