{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                </div>
                <div class="card-body">
                    <form method="post" id="prescription-form">
                        {% csrf_token %}

                        <h4>Prescription Details</h4>
                        {{ prescription_form.patient|as_crispy_field }}
                        {{ prescription_form.doctor|as_crispy_field }}
                        {{ prescription_form.prescription_date|as_crispy_field }}
                        {{ prescription_form.diagnosis|as_crispy_field }}
                        {{ prescription_form.prescription_type|as_crispy_field }}
                        {{ prescription_form.notes|as_crispy_field }}

                        <hr>
                        <h4>Medications</h4>
                        <div id="medication-items-container">
                            {{ medication_formset.management_form }}
                            {% for form in medication_formset %}
                                <div class="form-row medication-item mb-3 p-3 border rounded bg-light">
                                    {% for field in form %}
                                        <div class="col-md-4">
                                            {{ field.label_tag }}
                                            {{ field }}
                                            {% if field.help_text %}
                                                <small class="form-text text-muted">{{ field.help_text }}</small>
                                            {% endif %}
                                            {% for error in field.errors %}
                                                <div class="invalid-feedback d-block">{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endfor %}
                                    <div class="col-md-12 text-right mt-2">
                                        <button type="button" class="btn btn-danger btn-sm remove-medication-item"><i class="fas fa-trash"></i> Remove</button>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        <button type="button" class="btn btn-success btn-sm" id="add-medication-item"><i class="fas fa-plus"></i> Add Another Medication</button>

                        <div id="empty-form-template" style="display: none;">
                            <div class="form-row medication-item mb-3 p-3 border rounded bg-light">
                                {% for field in medication_formset.empty_form %}
                                    <div class="col-md-4">
                                        {{ field.label_tag }}
                                        {{ field }}
                                        {% if field.help_text %}
                                            <small class="form-text text-muted">{{ field.help_text }}</small>
                                        {% endif %}
                                        {% for error in field.errors %}
                                            <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endfor %}
                                <div class="col-md-12 text-right mt-2">
                                    <button type="button" class="btn btn-danger btn-sm remove-medication-item"><i class="fas fa-trash"></i> Remove</button>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">Create Prescription</button>
                            <a href="{% url 'pharmacy:prescriptions' %}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('.select2').select2();

        // Function to initialize Select2 on a new element
        function initializeSelect2(element) {
            $(element).select2();
        }

        // Add Medication Item
        $('#add-medication-item').click(function() {
            var newItem = $('.medication-item:first').clone();
            newItem.find('input, textarea').val(''); // Clear values
            newItem.find('select').val('').trigger('change'); // Clear selected option and trigger change for Select2
            newItem.find('.select2-container').remove(); // Remove old Select2 instance
            $('#medication-items-container').append(newItem);
            initializeSelect2(newItem.find('select')); // Initialize Select2 on the new select
        });

        // Remove Medication Item
        $(document).on('click', '.remove-medication-item', function() {
            if ($('.medication-item').length > 1) {
                $(this).closest('.medication-item').remove();
            } else {
                alert("You must have at least one medication item.");
            }
        });
    });
</script>
{% endblock %}