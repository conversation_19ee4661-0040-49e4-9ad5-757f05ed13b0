{"summary": {"total_tests": 36, "passed": 25, "failed": 0, "warnings": 11, "success_rate": 69.44444444444444}, "results": [{"test": "Valid<PERSON><PERSON><PERSON>", "test_type": "AUTHENTICATION", "status": "PASS", "message": "Valid credentials accepted", "error": null, "details": null, "timestamp": "2025-08-02T08:56:51.934659"}, {"test": "Invalid-Login", "test_type": "AUTHENTICATION", "status": "PASS", "message": "Invalid credentials correctly rejected", "error": null, "details": null, "timestamp": "2025-08-02T08:56:54.440492"}, {"test": "Client-Login", "test_type": "AUTHENTICATION", "status": "PASS", "message": "Client login successful", "error": null, "details": null, "timestamp": "2025-08-02T08:56:55.663275"}, {"test": "Superuser-Admin-Access", "test_type": "AUTHORIZATION", "status": "PASS", "message": "Superuser can access admin", "error": null, "details": "Status: 200", "timestamp": "2025-08-02T08:56:58.533333"}, {"test": "Regular-User-Admin-Denied", "test_type": "AUTHORIZATION", "status": "PASS", "message": "Regular user correctly denied admin access", "error": null, "details": "Status: 302", "timestamp": "2025-08-02T08:56:59.647867"}, {"test": "/-anonymous", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access granted", "error": null, "details": null, "timestamp": "2025-08-02T08:56:59.684848"}, {"test": "/-regular", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access granted", "error": null, "details": null, "timestamp": "2025-08-02T08:57:00.864951"}, {"test": "/-admin", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access granted", "error": null, "details": null, "timestamp": "2025-08-02T08:57:02.099974"}, {"test": "/-superuser", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access granted", "error": null, "details": null, "timestamp": "2025-08-02T08:57:03.492623"}, {"test": "/accounts/login/-anonymous", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access granted", "error": null, "details": null, "timestamp": "2025-08-02T08:57:03.531491"}, {"test": "/accounts/login/-regular", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access granted", "error": null, "details": null, "timestamp": "2025-08-02T08:57:04.807502"}, {"test": "/accounts/login/-admin", "test_type": "URL_ACCESS", "status": "WARN", "message": "Unexpected response: 302", "error": null, "details": null, "timestamp": "2025-08-02T08:57:06.080696"}, {"test": "/accounts/login/-superuser", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access granted", "error": null, "details": null, "timestamp": "2025-08-02T08:57:07.407411"}, {"test": "/dashboard/-anonymous", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access denied", "error": null, "details": null, "timestamp": "2025-08-02T08:57:07.446662"}, {"test": "/dashboard/-regular", "test_type": "URL_ACCESS", "status": "WARN", "message": "Unexpected response: 302", "error": null, "details": null, "timestamp": "2025-08-02T08:57:08.990768"}, {"test": "/dashboard/-admin", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access granted", "error": null, "details": null, "timestamp": "2025-08-02T08:57:10.199987"}, {"test": "/dashboard/-superuser", "test_type": "URL_ACCESS", "status": "WARN", "message": "Unexpected response: 302", "error": null, "details": null, "timestamp": "2025-08-02T08:57:11.627546"}, {"test": "/patients/-anonymous", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access denied", "error": null, "details": null, "timestamp": "2025-08-02T08:57:11.650257"}, {"test": "/patients/-regular", "test_type": "URL_ACCESS", "status": "WARN", "message": "Unexpected response: 302", "error": null, "details": null, "timestamp": "2025-08-02T08:57:13.339900"}, {"test": "/patients/-admin", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access granted", "error": null, "details": null, "timestamp": "2025-08-02T08:57:14.713456"}, {"test": "/patients/-superuser", "test_type": "URL_ACCESS", "status": "WARN", "message": "Unexpected response: 302", "error": null, "details": null, "timestamp": "2025-08-02T08:57:15.795835"}, {"test": "/doctors/-anonymous", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access denied", "error": null, "details": null, "timestamp": "2025-08-02T08:57:15.821023"}, {"test": "/doctors/-regular", "test_type": "URL_ACCESS", "status": "WARN", "message": "Unexpected response: 302", "error": null, "details": null, "timestamp": "2025-08-02T08:57:16.878140"}, {"test": "/doctors/-admin", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access granted", "error": null, "details": null, "timestamp": "2025-08-02T08:57:18.011727"}, {"test": "/doctors/-superuser", "test_type": "URL_ACCESS", "status": "WARN", "message": "Unexpected response: 302", "error": null, "details": null, "timestamp": "2025-08-02T08:57:19.043324"}, {"test": "/pharmacy/-anonymous", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access denied", "error": null, "details": null, "timestamp": "2025-08-02T08:57:19.071823"}, {"test": "/pharmacy/-regular", "test_type": "URL_ACCESS", "status": "WARN", "message": "Unexpected response: 302", "error": null, "details": null, "timestamp": "2025-08-02T08:57:20.082111"}, {"test": "/pharmacy/-admin", "test_type": "URL_ACCESS", "status": "WARN", "message": "Unexpected response: 404", "error": null, "details": null, "timestamp": "2025-08-02T08:57:21.156019"}, {"test": "/pharmacy/-superuser", "test_type": "URL_ACCESS", "status": "WARN", "message": "Unexpected response: 302", "error": null, "details": null, "timestamp": "2025-08-02T08:57:22.157956"}, {"test": "/admin/-anonymous", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access denied", "error": null, "details": null, "timestamp": "2025-08-02T08:57:22.185937"}, {"test": "/admin/-regular", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access denied", "error": null, "details": null, "timestamp": "2025-08-02T08:57:23.212030"}, {"test": "/admin/-admin", "test_type": "URL_ACCESS", "status": "PASS", "message": "Correct access granted", "error": null, "details": null, "timestamp": "2025-08-02T08:57:24.317853"}, {"test": "/admin/-superuser", "test_type": "URL_ACCESS", "status": "WARN", "message": "Unexpected response: 302", "error": null, "details": null, "timestamp": "2025-08-02T08:57:25.329840"}, {"test": "Session-Creation", "test_type": "SESSION_SECURITY", "status": "PASS", "message": "Session created successfully", "error": null, "details": null, "timestamp": "2025-08-02T08:57:26.361080"}, {"test": "Session-Persistence", "test_type": "SESSION_SECURITY", "status": "PASS", "message": "Session persists across requests", "error": null, "details": null, "timestamp": "2025-08-02T08:57:26.375907"}, {"test": "Session-<PERSON><PERSON>ut", "test_type": "SESSION_SECURITY", "status": "PASS", "message": "<PERSON><PERSON><PERSON> correctly invalidates session", "error": null, "details": null, "timestamp": "2025-08-02T08:57:26.387345"}]}