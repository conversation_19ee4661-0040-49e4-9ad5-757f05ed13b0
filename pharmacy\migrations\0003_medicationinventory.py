# Generated by Django 5.2 on 2025-06-28 09:39

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pharmacy', '0002_dispensary'),
    ]

    operations = [
        migrations.CreateModel(
            name='MedicationInventory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_quantity', models.IntegerField(default=0)),
                ('reorder_level', models.IntegerField(default=10)),
                ('last_restock_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('dispensary', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='medications', to='pharmacy.dispensary')),
                ('medication', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventories', to='pharmacy.medication')),
            ],
            options={
                'verbose_name_plural': 'Medication Inventories',
                'unique_together': {('medication', 'dispensary')},
            },
        ),
    ]
