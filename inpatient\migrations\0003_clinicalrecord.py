# Generated by Django 5.2 on 2025-06-27 16:41

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inpatient', '0002_bedtransfer_admission_bed_history_wardtransfer_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ClinicalRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('record_type', models.CharField(choices=[('vitals', 'Vital Signs'), ('medication', 'Medication Administration'), ('treatment', 'Treatment Plan'), ('progress', 'Progress Note'), ('other', 'Other')], max_length=50)),
                ('date_time', models.DateTimeField(default=django.utils.timezone.now)),
                ('notes', models.TextField()),
                ('temperature', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('blood_pressure_systolic', models.IntegerField(blank=True, null=True)),
                ('blood_pressure_diastolic', models.IntegerField(blank=True, null=True)),
                ('heart_rate', models.IntegerField(blank=True, null=True)),
                ('respiratory_rate', models.IntegerField(blank=True, null=True)),
                ('oxygen_saturation', models.IntegerField(blank=True, null=True)),
                ('medication_name', models.CharField(blank=True, max_length=255, null=True)),
                ('dosage', models.CharField(blank=True, max_length=100, null=True)),
                ('route', models.CharField(blank=True, max_length=100, null=True)),
                ('treatment_description', models.TextField(blank=True, null=True)),
                ('patient_condition', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('admission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='clinical_records', to='inpatient.admission')),
                ('recorded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-date_time'],
            },
        ),
    ]
