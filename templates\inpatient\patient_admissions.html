{% extends 'base.html' %}
{% load form_tags %}
{% load admission_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ title }}</h4>
                <a href="{% url 'inpatient:create_admission' %}?patient_id={{ patient.id }}" class="btn btn-light">
                    <i class="fas fa-plus-circle me-1"></i> New Admission
                </a>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2 mb-3">Patient Information</h5>
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Patient Name</th>
                                <td>{{ patient.get_full_name }}</td>
                            </tr>
                            <tr>
                                <th>Patient ID</th>
                                <td>{{ patient.patient_id }}</td>
                            </tr>
                            <tr>
                                <th>Gender</th>
                                <td>{{ patient.get_gender_display }}</td>
                            </tr>
                            <tr>
                                <th>Age</th>
                                <td>{{ patient.age }} years</td>
                            </tr>
                            <tr>
                                <th>Contact</th>
                                <td>{{ patient.phone }}</td>
                            </tr>
                            <tr>
                                <th>Blood Group</th>
                                <td>{{ patient.blood_group }}</td>
                            </tr>
                        </table>
                        <div class="mt-3">
                            <a href="{% url 'patients:detail' patient.id %}" class="btn btn-secondary">
                                <i class="fas fa-user me-1"></i> View Patient Profile
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2 mb-3">Admission Statistics</h5>
                        <div class="row text-center">
                            <div class="col-md-4 mb-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h3>{{ admissions.count }}</h3>
                                        <p class="mb-0">Total Admissions</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h3>{{ admissions|admitted_count }}</h3>
                                        <p class="mb-0">Currently Admitted</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h3>{{ admissions|discharged_count }}</h3>
                                        <p class="mb-0">Discharged</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% if current_admission %}
                            <div class="alert alert-success mt-3">
                                <i class="fas fa-info-circle me-2"></i>
                                Patient is currently admitted in
                                <strong>{{ current_admission.bed.ward.name }}</strong>
                                (Bed: {{ current_admission.bed.bed_number }})
                                <a href="{% url 'inpatient:admission_detail' current_admission.id %}" class="alert-link">
                                    View current admission
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <h5 class="border-bottom pb-2 mb-3">Admission History</h5>
                {% if admissions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Admission Date</th>
                                    <th>Discharge Date</th>
                                    <th>Ward/Bed</th>
                                    <th>Doctor</th>
                                    <th>Diagnosis</th>
                                    <th>Status</th>
                                    <th>Duration</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for admission in admissions %}
                                    <tr>
                                        <td>{{ admission.admission_date|date:"M d, Y H:i" }}</td>
                                        <td>
                                            {% if admission.discharge_date %}
                                                {{ admission.discharge_date|date:"M d, Y H:i" }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if admission.bed %}
                                                {{ admission.bed.ward.name }} / {{ admission.bed.bed_number }}
                                            {% else %}
                                                <span class="text-muted">Not assigned</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ admission.attending_doctor.get_full_name }}</td>
                                        <td>{{ admission.diagnosis|truncatechars:30 }}</td>
                                        <td>
                                            {% if admission.status == 'admitted' %}
                                                <span class="badge bg-success">Admitted</span>
                                            {% elif admission.status == 'discharged' %}
                                                <span class="badge bg-info">Discharged</span>
                                            {% elif admission.status == 'transferred' %}
                                                <span class="badge bg-warning">Transferred</span>
                                            {% elif admission.status == 'deceased' %}
                                                <span class="badge bg-danger">Deceased</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ admission.get_duration }} days</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{% url 'inpatient:admission_detail' admission.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                {% if admission.status == 'admitted' %}
                                                    <a href="{% url 'inpatient:discharge_patient' admission.id %}" class="btn btn-sm btn-info">
                                                        <i class="fas fa-procedures"></i> Discharge
                                                    </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No admission records found for this patient.
                        <a href="{% url 'inpatient:create_admission' %}?patient_id={{ patient.id }}" class="alert-link">
                            Create a new admission
                        </a>
                    </div>
                {% endif %}

                <div class="mt-3">
                    <a href="{% url 'inpatient:admissions' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to All Admissions
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
