{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Set New Password - Hospital Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card auth-form">
            <div class="card-body">
                <h2 class="card-title text-center mb-4">Set New Password</h2>
                
                {% if validlink %}
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="mb-3">
                            <label for="{{ form.new_password1.id_for_label }}" class="form-label">New Password</label>
                            {{ form.new_password1|add_class:"form-control" }}
                            {% if form.new_password1.errors %}
                                <div class="text-danger">
                                    {{ form.new_password1.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                {{ form.new_password1.help_text }}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.new_password2.id_for_label }}" class="form-label">Confirm New Password</label>
                            {{ form.new_password2|add_class:"form-control" }}
                            {% if form.new_password2.errors %}
                                <div class="text-danger">
                                    {{ form.new_password2.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Set New Password</button>
                        </div>
                    </form>
                {% else %}
                    <div class="alert alert-danger">
                        <p>The password reset link was invalid, possibly because it has already been used.</p>
                        <p>Please request a new password reset.</p>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{% url 'accounts:password_reset' %}" class="btn btn-primary">Request New Reset Link</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
