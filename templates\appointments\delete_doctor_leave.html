{% extends 'base.html' %}

{% block title %}Delete Leave Request - Hospital Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">Delete Leave Request</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    You are about to delete the following leave request:
                </div>
                
                <div class="text-center mb-4">
                    <i class="fas fa-calendar-times fa-5x text-danger mb-3"></i>
                    <h5>Leave Request for Dr. {{ leave.doctor.get_full_name }}</h5>
                    <p class="text-muted">
                        From {{ leave.start_date|date:"F d, Y" }} to {{ leave.end_date|date:"F d, Y" }}
                    </p>
                    <p class="text-muted">
                        <strong>Reason:</strong> {{ leave.reason }}
                    </p>
                    <p class="text-muted">
                        <strong>Status:</strong> 
                        {% if leave.is_approved %}
                            <span class="badge bg-success">Approved</span>
                        {% else %}
                            <span class="badge bg-warning">Pending</span>
                        {% endif %}
                    </p>
                </div>
                
                <p class="mb-4">
                    This action will delete the leave request. If the leave was approved, the doctor will be marked as available for appointments during this period again.
                    This action cannot be undone.
                </p>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'appointments:manage_doctor_leaves' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> No, Go Back
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Yes, Delete Leave Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
