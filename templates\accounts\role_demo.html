{% extends 'base.html' %}
{% load role_tags %}
{% block title %}HMS Role System Demo - Hospital Management System{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
    }
    .role-card {
        transition: transform 0.2s;
        border-left: 4px solid #28a745;
    }
    .role-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(45deg, #007bff, #28a745);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
    }
    .permission-badge {
        font-size: 0.75rem;
        margin: 2px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users-cog me-2"></i>HMS Role System Demo</h2>
                <div>
                    <a href="{% url 'accounts:role_management' %}" class="btn btn-primary me-2">
                        <i class="fas fa-cogs me-2"></i>Manage Roles
                    </a>
                    <a href="{% url 'accounts:user_dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-users me-2"></i>Manage Users
                    </a>
                </div>
            </div>

            <!-- Statistics Row -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card stats-card text-center">
                        <div class="card-body">
                            <h3>{{ total_roles }}</h3>
                            <p class="mb-0">Total Roles</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stats-card text-center">
                        <div class="card-body">
                            <h3>{{ total_users }}</h3>
                            <p class="mb-0">Total Users</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stats-card text-center">
                        <div class="card-body">
                            <h3>{{ users_with_roles }}</h3>
                            <p class="mb-0">Users with Roles</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stats-card text-center">
                        <div class="card-body">
                            <h3>{{ permissions_in_use }}</h3>
                            <p class="mb-0">Permissions in Use</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role Overview -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>How to Use the Role System</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-user-plus me-2"></i>Assigning Roles to Users</h6>
                            <ol>
                                <li>Go to <a href="{% url 'accounts:user_dashboard' %}">User Management</a></li>
                                <li>Click the <i class="fas fa-user-cog text-success"></i> button next to any user</li>
                                <li>Select the appropriate roles for that user</li>
                                <li>Click "Update Privileges" to save</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-cogs me-2"></i>Managing Roles</h6>
                            <ol>
                                <li>Go to <a href="{% url 'accounts:role_management' %}">Role Management</a></li>
                                <li>Create new roles or edit existing ones</li>
                                <li>Assign permissions to roles</li>
                                <li>Set up role hierarchies if needed</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Roles and Users -->
            <div class="row">
                {% for role in roles %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card role-card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-user-tag me-2"></i>{{ role.name|title }}
                            </h6>
                            <span class="badge bg-primary">{{ role.user_count }} user{{ role.user_count|pluralize }}</span>
                        </div>
                        <div class="card-body">
                            {% if role.description %}
                                <p class="text-muted small mb-3">{{ role.description|truncatechars:100 }}</p>
                            {% endif %}

                            <!-- Users with this role -->
                            {% if role.user_count > 0 %}
                                <h6 class="text-primary mb-2">Assigned Users:</h6>
                                {% for user in users_by_role|lookup:role.name %}
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="user-avatar me-2">
                                            {{ user.first_name|first|default:user.username|first }}
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold">{{ user.get_full_name|default:user.username }}</div>
                                            {% if user.profile.department %}
                                                <small class="text-muted">{{ user.profile.department }}</small>
                                            {% endif %}
                                        </div>
                                        <a href="{% url 'accounts:user_privileges' user.id %}" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="Manage Privileges">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No users assigned to this role yet.
                                </div>
                            {% endif %}

                            <!-- Permissions preview -->
                            {% if role.permissions.exists %}
                                <hr>
                                <h6 class="text-success mb-2">Permissions ({{ role.permissions.count }}):</h6>
                                <div style="max-height: 150px; overflow-y: auto;">
                                    {% for permission in role.permissions.all|slice:":5" %}
                                        <span class="badge bg-light text-dark permission-badge">{{ permission.name|truncatechars:20 }}</span>
                                    {% endfor %}
                                    {% if role.permissions.count > 5 %}
                                        <span class="badge bg-secondary permission-badge">+{{ role.permissions.count|add:"-5" }} more</span>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="card-footer">
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'accounts:edit_role' role.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit me-1"></i>Edit Role
                                </a>
                                <small class="text-muted align-self-center">
                                    {{ role.permissions.count }} permission{{ role.permissions.count|pluralize }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No roles found. <a href="{% url 'accounts:create_role' %}">Create your first role</a>.
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{% url 'accounts:create_role' %}" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-plus me-2"></i>Create New Role
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'accounts:user_dashboard' %}" class="btn btn-success w-100 mb-2">
                                <i class="fas fa-user-plus me-2"></i>Assign Roles to Users
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'accounts:permission_management' %}" class="btn btn-info w-100 mb-2">
                                <i class="fas fa-key me-2"></i>Manage Permissions
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'accounts:audit_logs' %}" class="btn btn-warning w-100 mb-2">
                                <i class="fas fa-history me-2"></i>View Audit Logs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add tooltips to all buttons
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
