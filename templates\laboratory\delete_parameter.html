{% extends 'base.html' %}

{% block title %}Delete Parameter - Hospital Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">Delete Parameter</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    You are about to delete the following parameter:
                </div>
                
                <div class="text-center mb-4">
                    <i class="fas fa-vial fa-5x text-danger mb-3"></i>
                    <h5>{{ parameter.name }}</h5>
                    <p class="text-muted">
                        <strong>Test:</strong> {{ test.name }}
                    </p>
                    <p class="text-muted">
                        <strong>Normal Range:</strong> {{ parameter.normal_range }}
                    </p>
                    <p class="text-muted">
                        <strong>Unit:</strong> {{ parameter.unit }}
                    </p>
                </div>
                
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Warning:</strong> This action will permanently delete this parameter. This action cannot be undone.
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'laboratory:edit_test' test.id %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> No, Go Back
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Yes, Delete Parameter
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
