{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Theatre{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Delete Theatre</h1>
        <a href="{% url 'theatre:theatre_list' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>

    <!-- Delete Confirmation Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">Confirm Deletion</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle mr-2"></i> Warning!</h5>
                <p>You are about to delete the theatre <strong>"{{ object.name }}"</strong>. This action cannot be undone.</p>
                <p>All surgeries associated with this theatre will be affected.</p>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    Theatre Details
                </div>
                <div class="card-body">
                    <p><strong>Name:</strong> {{ object.name }}</p>
                    <p><strong>Theatre Number:</strong> {{ object.theatre_number }}</p>
                    <p><strong>Floor:</strong> {{ object.floor }}</p>
                </div>
            </div>
            
            <form method="post">
                {% csrf_token %}
                <div class="text-center">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash mr-2"></i> Confirm Delete
                    </button>
                    <a href="{% url 'theatre:theatre_detail' object.id %}" class="btn btn-secondary">
                        <i class="fas fa-times mr-2"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}