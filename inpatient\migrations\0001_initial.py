# Generated by Django 5.2 on 2025-05-31 08:03

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Bed',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bed_number', models.CharField(max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_occupied', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Admission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('admission_date', models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ('discharge_date', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('diagnosis', models.TextField()),
                ('status', models.CharField(choices=[('admitted', 'Admitted'), ('discharged', 'Discharged'), ('transferred', 'Transferred'), ('deceased', 'Deceased')], db_index=True, default='admitted', max_length=20)),
                ('reason_for_admission', models.TextField()),
                ('admission_notes', models.TextField(blank=True, null=True)),
                ('discharge_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('attending_doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attending_admissions', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_admissions', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='admissions', to='patients.patient')),
                ('bed', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='admissions', to='inpatient.bed')),
            ],
        ),
        migrations.CreateModel(
            name='DailyRound',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_time', models.DateTimeField(default=django.utils.timezone.now)),
                ('notes', models.TextField()),
                ('treatment_instructions', models.TextField(blank=True, null=True)),
                ('medication_instructions', models.TextField(blank=True, null=True)),
                ('diet_instructions', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('admission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_rounds', to='inpatient.admission')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='doctor_rounds', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-date_time'],
            },
        ),
        migrations.CreateModel(
            name='NursingNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_time', models.DateTimeField(default=django.utils.timezone.now)),
                ('notes', models.TextField()),
                ('vital_signs', models.TextField(blank=True, null=True)),
                ('medication_given', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('admission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='nursing_notes', to='inpatient.admission')),
                ('nurse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='nurse_notes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-date_time'],
            },
        ),
        migrations.CreateModel(
            name='Ward',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('ward_type', models.CharField(choices=[('general', 'General Ward'), ('private', 'Private Ward'), ('semi_private', 'Semi-Private Ward'), ('icu', 'Intensive Care Unit'), ('nicu', 'Neonatal ICU'), ('picu', 'Pediatric ICU'), ('emergency', 'Emergency Ward'), ('maternity', 'Maternity Ward'), ('pediatric', 'Pediatric Ward'), ('psychiatric', 'Psychiatric Ward'), ('isolation', 'Isolation Ward'), ('a_and_e', 'A & E'), ('epu', 'EPU'), ('g_e', 'G/E (Gynaecology emergency)'), ('scbu', 'SCBU'), ('theater', 'Theater'), ('nhia', 'NHIA (For Insurance)'), ('opthalmic', 'Opthalmic'), ('ent', 'ENT'), ('dental', 'Dental'), ('gopd', 'GOPD'), ('anc', 'ANC'), ('oncology', 'Oncology'), ('physiotherapy', 'Physiotherapy'), ('retainership', 'Retainership'), ('arvc', 'ARVC')], max_length=20)),
                ('floor', models.CharField(max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('capacity', models.IntegerField()),
                ('charge_per_day', models.DecimalField(decimal_places=2, max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('primary_doctor', models.ForeignKey(blank=True, help_text='Primary doctor responsible for this ward', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='primary_wards', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='bed',
            name='ward',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='beds', to='inpatient.ward'),
        ),
        migrations.AlterUniqueTogether(
            name='bed',
            unique_together={('ward', 'bed_number')},
        ),
    ]
