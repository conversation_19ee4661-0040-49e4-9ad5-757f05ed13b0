{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Manage Availability - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Manage Availability</h1>
        <a href="{% url 'doctors:doctor_profile' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-1"></i> Back to Profile
        </a>
    </div>

    <div class="row">
        <!-- Add Availability Form -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Add Availability Slot</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.weekday.id_for_label }}" class="form-label">Day of Week *</label>
                            {{ form.weekday|add_class:"form-select" }}
                            {% if form.weekday.errors %}
                                <div class="text-danger">{{ form.weekday.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.start_time.id_for_label }}" class="form-label">Start Time *</label>
                            {{ form.start_time|add_class:"form-control" }}
                            {% if form.start_time.errors %}
                                <div class="text-danger">{{ form.start_time.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.end_time.id_for_label }}" class="form-label">End Time *</label>
                            {{ form.end_time|add_class:"form-control" }}
                            {% if form.end_time.errors %}
                                <div class="text-danger">{{ form.end_time.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.max_appointments.id_for_label }}" class="form-label">Max Appointments</label>
                            {{ form.max_appointments|add_class:"form-control" }}
                            <small class="form-text text-muted">{{ form.max_appointments.help_text }}</small>
                            {% if form.max_appointments.errors %}
                                <div class="text-danger">{{ form.max_appointments.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_available }}
                                <label class="form-check-label" for="{{ form.is_available.id_for_label }}">
                                    Available
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus-circle mr-1"></i> Add Slot
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Availability List -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Availability</h6>
                </div>
                <div class="card-body">
                    {% if availability %}
                        <div class="table-responsive">
                            <table class="table table-bordered" id="availabilityTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Day</th>
                                        <th>Start Time</th>
                                        <th>End Time</th>
                                        <th>Max Appointments</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for slot in availability %}
                                        <tr>
                                            <td>{{ slot.get_weekday_display }}</td>
                                            <td>{{ slot.start_time|time:"g:i A" }}</td>
                                            <td>{{ slot.end_time|time:"g:i A" }}</td>
                                            <td>{{ slot.max_appointments }}</td>
                                            <td>
                                                {% if slot.is_available %}
                                                    <span class="badge bg-success text-white">Available</span>
                                                {% else %}
                                                    <span class="badge bg-danger text-white">Not Available</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <form method="post" action="{% url 'doctors:delete_availability' slot.id %}">
                                                    {% csrf_token %}
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this availability slot?');">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No availability slots found.</p>
                            <p class="text-muted">Add your availability to allow patients to book appointments with you.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#availabilityTable').DataTable({
            "order": [[0, "asc"], [1, "asc"]]
        });
    });
</script>
{% endblock %}
