# Generated by Django 5.2 on 2025-05-31 08:03

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('billing', '0001_initial'),
        ('patients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MedicationCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Medication Categories',
            },
        ),
        migrations.CreateModel(
            name='Medication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('generic_name', models.CharField(blank=True, max_length=100, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('dosage_form', models.CharField(max_length=50)),
                ('strength', models.CharField(max_length=50)),
                ('manufacturer', models.CharField(blank=True, max_length=100, null=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('stock_quantity', models.IntegerField(default=0)),
                ('reorder_level', models.IntegerField(default=10)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('side_effects', models.TextField(blank=True, null=True)),
                ('precautions', models.TextField(blank=True, null=True)),
                ('storage_instructions', models.CharField(blank=True, max_length=200, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='medications', to='pharmacy.medicationcategory')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Prescription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prescription_date', models.DateField(default=django.utils.timezone.now)),
                ('diagnosis', models.CharField(blank=True, max_length=200, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('dispensed', 'Dispensed'), ('partially_dispensed', 'Partially Dispensed'), ('cancelled', 'Cancelled'), ('on_hold', 'On Hold')], default='pending', max_length=20)),
                ('payment_status', models.CharField(choices=[('unpaid', 'Unpaid'), ('paid', 'Paid'), ('waived', 'Waived')], default='unpaid', max_length=20)),
                ('prescription_type', models.CharField(choices=[('inpatient', 'In-Patient (MAR/eMAR)'), ('outpatient', 'Out-Patient (Take-Home)')], default='outpatient', help_text='Is this an in-patient (MAR/eMAR) or out-patient (take-home) prescription?', max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='doctor_prescriptions', to=settings.AUTH_USER_MODEL)),
                ('invoice', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='prescription_invoice', to='billing.invoice')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prescriptions', to='patients.patient')),
            ],
        ),
        migrations.CreateModel(
            name='PrescriptionItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dosage', models.CharField(max_length=100)),
                ('frequency', models.CharField(max_length=100)),
                ('duration', models.CharField(max_length=100)),
                ('instructions', models.TextField(blank=True, null=True)),
                ('quantity', models.IntegerField()),
                ('quantity_dispensed_so_far', models.IntegerField(default=0)),
                ('is_dispensed', models.BooleanField(default=False)),
                ('dispensed_date', models.DateTimeField(blank=True, null=True)),
                ('dispensed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='last_dispensed_items', to=settings.AUTH_USER_MODEL)),
                ('medication', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pharmacy.medication')),
                ('prescription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='pharmacy.prescription')),
            ],
        ),
        migrations.CreateModel(
            name='DispensingLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dispensed_quantity', models.IntegerField()),
                ('dispensed_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('unit_price_at_dispense', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_price_for_this_log', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('dispensed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dispensing_actions', to=settings.AUTH_USER_MODEL)),
                ('prescription_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dispensing_logs', to='pharmacy.prescriptionitem')),
            ],
            options={
                'verbose_name': 'Dispensing Log Entry',
                'verbose_name_plural': 'Dispensing Log Entries',
                'ordering': ['-dispensed_date'],
            },
        ),
        migrations.CreateModel(
            name='Purchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('purchase_date', models.DateField()),
                ('invoice_number', models.CharField(max_length=50, unique=True)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('partial', 'Partial'), ('paid', 'Paid')], default='pending', max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('approval_status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled')], db_index=True, default='draft', max_length=20)),
                ('approval_notes', models.TextField(blank=True, null=True)),
                ('approval_updated_at', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('current_approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='pending_purchase_approvals', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='PurchaseApproval',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('comments', models.TextField(blank=True, null=True)),
                ('decided_at', models.DateTimeField(blank=True, null=True)),
                ('step_order', models.PositiveIntegerField(default=1)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('approver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='approvals', to='pharmacy.purchase')),
            ],
            options={
                'ordering': ['step_order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField()),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True)),
                ('expiry_date', models.DateField()),
                ('medication', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pharmacy.medication')),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='pharmacy.purchase')),
            ],
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('phone_number', models.CharField(max_length=15)),
                ('address', models.TextField()),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=100)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('country', models.CharField(default='India', max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='pharmacy_su_name_0d0ac4_idx')],
            },
        ),
        migrations.AddField(
            model_name='purchase',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchases', to='pharmacy.supplier'),
        ),
        migrations.AddIndex(
            model_name='medication',
            index=models.Index(fields=['name'], name='pharmacy_me_name_46a660_idx'),
        ),
        migrations.AddIndex(
            model_name='medication',
            index=models.Index(fields=['category'], name='pharmacy_me_categor_e2528a_idx'),
        ),
        migrations.AddIndex(
            model_name='medication',
            index=models.Index(fields=['is_active'], name='pharmacy_me_is_acti_1b417e_idx'),
        ),
        migrations.AddIndex(
            model_name='medication',
            index=models.Index(fields=['stock_quantity'], name='pharmacy_me_stock_q_83487e_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='purchaseapproval',
            unique_together={('purchase', 'approver', 'step_order')},
        ),
    ]
