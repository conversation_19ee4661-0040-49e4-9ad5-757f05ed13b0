# Generated by Django 5.1.5 on 2025-08-01 06:31

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inpatient', '0005_admission_amount_paid'),
        ('pharmacy', '0006_purchase_dispensary'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='InpatientMedication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('is_paid', models.BooleanField(default=False)),
                ('payment_source', models.CharField(blank=True, choices=[('billing_office', 'Billing Office'), ('patient_wallet', 'Patient Wallet')], max_length=20, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('admission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='medications', to='inpatient.admission')),
                ('ordered_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inpatient_medication_orders', to=settings.AUTH_USER_MODEL)),
                ('prescription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inpatient_medications', to='pharmacy.prescription')),
            ],
            options={
                'ordering': ['-order_date'],
            },
        ),
    ]
