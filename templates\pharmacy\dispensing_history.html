{% extends 'base.html' %}
{% load pharmacy_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ title }}</h4>
                <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-light btn-sm">
                    <i class="fas fa-arrow-left"></i> Back to Prescription Details
                </a>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>Prescription ID:</strong> {{ prescription.id }}</p>
                        <p><strong>Patient:</strong> {{ prescription.patient.get_full_name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Date Prescribed:</strong> {{ prescription.prescription_date|date:"F d, Y" }}</p>
                        <p><strong>Doctor:</strong> Dr. {{ prescription.doctor.get_full_name }}</p>
                    </div>
                </div>

                {% if dispensing_logs %}
                    <div class="table-responsive">
                        <table class="table table-hover table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Medication</th>
                                    <th>Dispensed Qty</th>
                                    <th>Unit Price</th>
                                    <th>Total Price</th>
                                    <th>Dispensed By</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in dispensing_logs %}
                                    <tr>
                                        <td>{{ log.dispensed_date|date:"Y-m-d H:i:s" }}</td>
                                        <td>
                                            {{ log.prescription_item.medication.name }} 
                                            ({{ log.prescription_item.medication.strength }} - {{ log.prescription_item.medication.dosage_form }})
                                        </td>
                                        <td>{{ log.dispensed_quantity }}</td>
                                        <td>{{ log.unit_price_at_dispense|floatformat:2 }}</td>
                                        <td>{{ log.total_price_for_this_log|floatformat:2 }}</td>
                                        <td>{{ log.dispensed_by.get_full_name|default:log.dispensed_by.username }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No dispensing history found for this prescription.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
