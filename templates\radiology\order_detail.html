{% extends 'base.html' %}
{% load radiology_tags %}

{% block title %}Radiology Order Details - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Radiology Order #{{ order_id }}</h1>
        <a href="{% url 'radiology:index' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Dashboard
        </a>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Order Information</h6>
                    <div>
                        {% if order %}
                            <span class="mr-2">{{ order.status|radiology_status_badge }}</span>
                            <span>{{ order.priority|priority_badge }}</span>
                        {% else %}
                            <span class="mr-2">Unknown Status</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="font-weight-bold">Patient Information</h5>
                            {% if order %}
                                <p><strong>Name:</strong> {{ order.patient.get_full_name }}</p>
                                <p><strong>Patient ID:</strong> {{ order.patient.patient_id }}</p>
                                <p><strong>Gender:</strong> {{ order.patient.get_gender_display }}</p>
                                <p><strong>Age:</strong> {{ order.patient.age }} years</p>
                            {% else %}
                                <p>Unknown Patient</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h5 class="font-weight-bold">Order Details</h5>
                            {% if order %}
                                <p><strong>Test:</strong> {{ order.test.name }}</p>
                                <p><strong>Order Date:</strong> {{ order.order_date|date:"F j, Y H:i" }}</p>
                                <p><strong>Scheduled Date:</strong> {{ order.scheduled_date|date:"F j, Y H:i"|default:"Not scheduled yet" }}</p>
                                <p><strong>Referring Doctor:</strong> Dr. {{ order.referring_doctor.get_full_name }}</p>
                            {% else %}
                                <p>Unknown Order</p>
                            {% endif %}
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h5 class="font-weight-bold">Clinical Information</h5>
                            <p>{% if order %}{{ order.clinical_information|default:"-" }}{% else %}-{% endif %}</p>
                            <h5 class="font-weight-bold mt-4">Additional Notes</h5>
                            <p>{% if order %}{{ order.notes|default:"-" }}{% else %}-{% endif %}</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h5 class="font-weight-bold">Actions</h5>
                            <div class="alert alert-warning mb-3" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Tip:</strong> Use the action buttons below to manage this order. Critical actions (Complete, Cancel) will ask for confirmation.
                            </div>
                            <div class="btn-group" role="group">
                                {% if order %}
                                    {% if order.status == 'pending' or order.status == 'scheduled' %}
                                        <form method="post" action="{% url 'radiology:schedule_order' order.id %}" style="display:inline;" onsubmit="return confirm('Are you sure you want to schedule this order?');">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-calendar-alt mr-1"></i> Schedule
                                            </button>
                                        </form>
                                    {% endif %}
                                    {% if order.status != 'completed' and order.status != 'cancelled' %}
                                        <form method="post" action="{% url 'radiology:mark_completed' order.id %}" style="display:inline;" onsubmit="return confirm('Mark this order as completed?');">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-success">
                                                <i class="fas fa-check-circle mr-1"></i> Mark as Completed
                                            </button>
                                        </form>
                                    {% endif %}
                                    {% if not result and order.status == 'completed' %}
                                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addResultModal">
                                            <i class="fas fa-file-medical-alt mr-1"></i> Add Results
                                        </button>
                                    {% elif result %}
                                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addResultModal">
                                            <i class="fas fa-file-medical-alt mr-1"></i> Edit Results
                                        </button>
                                    {% endif %}
                                    <a href="{% url 'radiology:edit_order' order.id %}" class="btn btn-warning">
                                        <i class="fas fa-edit mr-1"></i> Edit
                                    </a>
                                    {% if order.status != 'cancelled' %}
                                        <form method="post" action="{% url 'radiology:cancel_order' order.id %}" style="display:inline;" onsubmit="return confirm('Are you sure you want to cancel this order? This action cannot be undone.');">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-danger">
                                                <i class="fas fa-times-circle mr-1"></i> Cancel
                                            </button>
                                        </form>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">No actions available.</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Results</h6>
                </div>
                <div class="card-body">
                    {% if result %}
                        <h5>Performed By: Dr. {{ result.performed_by.get_full_name }}</h5>
                        <p><strong>Date:</strong> {{ result.result_date|date:"F j, Y H:i" }}</p>
                        <p><strong>Findings:</strong> {{ result.findings }}</p>
                        <p><strong>Impression:</strong> {{ result.impression }}</p>
                        {% if result.image_file %}
                            <p><strong>Image:</strong> <a href="{{ result.image_file.url }}" target="_blank">View Image</a></p>
                        {% endif %}
                        <p><strong>Abnormal:</strong> {{ result.is_abnormal|yesno:"Yes,No" }}</p>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-file-medical-alt fa-4x text-gray-300 mb-3"></i>
                            <p class="lead">No results have been added yet.</p>
                            {% if order and order.status == 'completed' %}
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addResultModal">
                                    <i class="fas fa-plus mr-1"></i> Add Results
                                </button>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    <!-- Add/Edit Result Modal -->
    <div class="modal fade" id="addResultModal" tabindex="-1" aria-labelledby="addResultModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                {% if order %}
                <form method="post" action="{% url 'radiology:add_result' order.id %}" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="modal-header">
                        <h5 class="modal-title" id="addResultModalLabel">{% if result %}Edit{% else %}Add{% endif %} Radiology Result</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="performed_by" class="form-label">Performed By</label>
                            <input type="text" class="form-control" id="performed_by" name="performed_by" value="{{ request.user.get_full_name }}" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="findings" class="form-label">Findings</label>
                            <textarea class="form-control" id="findings" name="findings" rows="3">{% if result %}{{ result.findings }}{% endif %}</textarea>
                        </div>
                        <div class="mb-3">
                            <label for="impression" class="form-label">Impression</label>
                            <textarea class="form-control" id="impression" name="impression" rows="2">{% if result %}{{ result.impression }}{% endif %}</textarea>
                        </div>
                        <div class="mb-3">
                            <label for="image_file" class="form-label">Image (optional)</label>
                            <input type="file" class="form-control" id="image_file" name="image_file">
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" value="1" id="is_abnormal" name="is_abnormal" {% if result and result.is_abnormal %}checked{% endif %}>
                            <label class="form-check-label" for="is_abnormal">Abnormal</label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Save Result</button>
                    </div>
                </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
