{% extends 'base.html' %}

{% block title %}Delete Category - Hospital Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">Delete Category</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    You are about to delete the following category:
                </div>
                
                <div class="text-center mb-4">
                    <i class="fas fa-tags fa-5x text-danger mb-3"></i>
                    <h5>{{ category.name }}</h5>
                    <p class="text-muted">
                        {{ category.description }}
                    </p>
                    <p class="text-muted">
                        <strong>Medications in this category:</strong> {{ category.medications.count }}
                    </p>
                </div>
                
                {% if category.medications.exists %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        This category cannot be deleted because it contains medications. You must first reassign or delete all medications in this category.
                    </div>
                {% else %}
                    <p class="mb-4">
                        This action will permanently delete this category. This action cannot be undone.
                    </p>
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'pharmacy:manage_categories' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> No, Go Back
                        </a>
                        <button type="submit" class="btn btn-danger" {% if category.medications.exists %}disabled{% endif %}>
                            <i class="fas fa-trash"></i> Yes, Delete Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
