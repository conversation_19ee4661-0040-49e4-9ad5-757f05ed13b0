{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ title }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Patients</h6>
        </div>
        <div class="card-body">
            <form method="get">
                <div class="row">
                    <div class="col-md-4">{{ form.start_date.label_tag }} {{ form.start_date }}</div>
                    <div class="col-md-4">{{ form.end_date.label_tag }} {{ form.end_date }}</div>
                    <div class="col-md-4">{{ form.gender.label_tag }} {{ form.gender }}</div>
                </div>
                <button type="submit" class="btn btn-primary mt-3">Filter</button>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Patient List</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Date of Birth</th>
                            <th>Gender</th>
                            <th>Contact</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for patient in page_obj %}
                        <tr>
                            <td>{{ patient.id }}</td>
                            <td>{{ patient.get_full_name }}</td>
                            <td>{{ patient.date_of_birth }}</td>
                            <td>{{ patient.get_gender_display }}</td>
                            <td>{{ patient.phone_number }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">No patients found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% include 'pagination.html' %}
        </div>
    </div>
</div>
{% endblock %}