{"headers": {"SECURE_BROWSER_XSS_FILTER": true, "SECURE_CONTENT_TYPE_NOSNIFF": true, "SECURE_HSTS_SECONDS": 31536000, "SECURE_HSTS_INCLUDE_SUBDOMAINS": true, "SECURE_HSTS_PRELOAD": true, "X_FRAME_OPTIONS": "DENY"}, "session": {"SESSION_COOKIE_SECURE": true, "SESSION_COOKIE_HTTPONLY": true, "SESSION_COOKIE_SAMESITE": "Strict", "CSRF_COOKIE_SECURE": true, "CSRF_COOKIE_HTTPONLY": true}, "password": {"AUTH_PASSWORD_VALIDATORS": ["django.contrib.auth.password_validation.UserAttributeSimilarityValidator", "django.contrib.auth.password_validation.MinimumLengthValidator", "django.contrib.auth.password_validation.CommonPasswordValidator", "django.contrib.auth.password_validation.NumericPasswordValidator"]}}