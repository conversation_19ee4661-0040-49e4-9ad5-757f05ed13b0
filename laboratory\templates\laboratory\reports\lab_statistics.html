{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        border-left: 4px solid;
        transition: transform 0.2s;
    }
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .stats-card.primary { border-left-color: #4e73df; }
    .stats-card.success { border-left-color: #1cc88a; }
    .stats-card.info { border-left-color: #36b9cc; }
    .stats-card.warning { border-left-color: #f6c23e; }
    
    .filter-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            <button onclick="window.print()" class="btn btn-primary btn-sm">
                <i class="fas fa-print"></i> Print Report
            </button>
            <button onclick="exportToCSV()" class="btn btn-success btn-sm">
                <i class="fas fa-file-csv"></i> Export CSV
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="card filter-card shadow mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-2">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-2">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-2">
                    <label for="test_category" class="form-label">Test Category</label>
                    <select class="form-control" id="test_category" name="test_category">
                        <option value="">All Categories</option>
                        {% for category in test_categories %}
                        <option value="{{ category.id }}" {% if category.id|stringformat:"s" == selected_category %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="pending" {% if selected_status == 'pending' %}selected{% endif %}>Pending</option>
                        <option value="in_progress" {% if selected_status == 'in_progress' %}selected{% endif %}>In Progress</option>
                        <option value="completed" {% if selected_status == 'completed' %}selected{% endif %}>Completed</option>
                        <option value="cancelled" {% if selected_status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="priority" class="form-label">Priority</label>
                    <select class="form-control" id="priority" name="priority">
                        <option value="">All Priorities</option>
                        <option value="normal" {% if selected_priority == 'normal' %}selected{% endif %}>Normal</option>
                        <option value="urgent" {% if selected_priority == 'urgent' %}selected{% endif %}>Urgent</option>
                        <option value="emergency" {% if selected_priority == 'emergency' %}selected{% endif %}>Emergency</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-light w-100">
                        <i class="fas fa-filter"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Overall Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₦{{ overall_stats.total_revenue|floatformat:2|default:"0.00" }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Test Requests</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ overall_stats.total_requests|default:"0" }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-flask fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Completion Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ completion_rate|floatformat:1 }}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Avg Revenue per Test</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₦{{ overall_stats.avg_revenue_per_request|floatformat:2|default:"0.00" }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Categories Performance -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Performance by Test Category</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Total Requests</th>
                                    <th>Total Revenue</th>
                                    <th>Avg Price</th>
                                    <th>Unique Patients</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in category_stats %}
                                <tr>
                                    <td><strong>{{ stat.tests__category__name|default:"Unknown" }}</strong></td>
                                    <td>{{ stat.total_requests|default:"0" }}</td>
                                    <td>₦{{ stat.total_revenue|floatformat:2|default:"0.00" }}</td>
                                    <td>₦{{ stat.avg_price|floatformat:2|default:"0.00" }}</td>
                                    <td>{{ stat.unique_patients|default:"0" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No test data found for the selected period</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Status Distribution</h6>
                </div>
                <div class="card-body">
                    {% for status in status_stats %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-capitalize">{{ status.status }}</span>
                            <span class="font-weight-bold">{{ status.count }}</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: {% widthratio status.count overall_stats.total_requests 100 %}%"></div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Top Tests and Doctors -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Tests by Volume</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Test</th>
                                    <th>Requests</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for test in top_tests %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ test.tests__name }}</td>
                                    <td>{{ test.total_requests }}</td>
                                    <td>₦{{ test.total_revenue|floatformat:0 }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No data</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Requesting Doctors</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Doctor</th>
                                    <th>Requests</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for doctor in top_doctors %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ doctor.doctor__first_name }} {{ doctor.doctor__last_name }}</td>
                                    <td>{{ doctor.total_requests }}</td>
                                    <td>₦{{ doctor.total_revenue|floatformat:0 }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No data</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-primary">{{ overall_stats.unique_patients|default:"0" }}</h4>
                            <small class="text-muted">Unique Patients Served</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info">{{ overall_stats.unique_tests|default:"0" }}</h4>
                            <small class="text-muted">Different Tests Performed</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success">{{ overall_stats.unique_doctors|default:"0" }}</h4>
                            <small class="text-muted">Doctors Making Requests</small>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning">{{ start_date|timesince:end_date }}</h4>
                            <small class="text-muted">Reporting Period</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportToCSV() {
    alert('CSV export functionality will be implemented');
}
</script>
{% endblock %}
