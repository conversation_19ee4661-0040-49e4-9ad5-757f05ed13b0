{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">Supplier Name</label>
                            {{ form.name|add_class:"form-control" }}
                            {% if form.name.errors %}
                                <div class="text-danger">
                                    {{ form.name.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="{{ form.contact_person.id_for_label }}" class="form-label">Contact Person</label>
                            {{ form.contact_person|add_class:"form-control" }}
                            {% if form.contact_person.errors %}
                                <div class="text-danger">
                                    {{ form.contact_person.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                            {{ form.email|add_class:"form-control" }}
                            {% if form.email.errors %}
                                <div class="text-danger">
                                    {{ form.email.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                            {{ form.phone_number|add_class:"form-control" }}
                            {% if form.phone_number.errors %}
                                <div class="text-danger">
                                    {{ form.phone_number.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-12 mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">Address</label>
                            {{ form.address|add_class:"form-control" }}
                            {% if form.address.errors %}
                                <div class="text-danger">
                                    {{ form.address.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="{{ form.city.id_for_label }}" class="form-label">City</label>
                            {{ form.city|add_class:"form-control" }}
                            {% if form.city.errors %}
                                <div class="text-danger">
                                    {{ form.city.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="{{ form.state.id_for_label }}" class="form-label">State</label>
                            {{ form.state|add_class:"form-control" }}
                            {% if form.state.errors %}
                                <div class="text-danger">
                                    {{ form.state.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Postal Code field removed as per requirements -->

                        <div class="col-md-12 mb-3">
                            <label for="{{ form.country.id_for_label }}" class="form-label">Country</label>
                            {{ form.country|add_class:"form-control" }}
                            {% if form.country.errors %}
                                <div class="text-danger">
                                    {{ form.country.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-12 mb-3">
                            <div class="form-check form-switch">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                                <div class="text-danger">
                                    {{ form.is_active.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'pharmacy:manage_suppliers' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Suppliers
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Supplier
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
