{% extends 'base.html' %}
{% load static %}

{% block title %}Team Member Details{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Team Member Details</h2>
        <div>
            <a href="{% url 'theatre:team_update' object.pk %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{% url 'theatre:team_delete' object.pk %}" class="btn btn-danger delete-confirm">
                <i class="fas fa-trash"></i> Delete
            </a>
            <a href="{% url 'theatre:team_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Team Member Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Staff Member:</strong>
                            <p>{{ object.staff.get_full_name }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>Role:</strong>
                            <p><span class="badge badge-info">{{ object.get_role_display }}</span></p>
                        </div>
                    </div>
                    
                    {% if object.usage_notes %}
                    <div class="row">
                        <div class="col-12">
                            <strong>Notes:</strong>
                            <p>{{ object.usage_notes|linebreaks }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Surgery Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Patient:</strong><br>
                    <a href="{% url 'patients:detail' object.surgery.patient.id %}">
                        {{ object.surgery.patient.get_full_name }}
                    </a></p>
                    
                    <p><strong>Surgery Type:</strong><br>
                    {{ object.surgery.surgery_type.name }}</p>
                    
                    <p><strong>Scheduled Date:</strong><br>
                    {{ object.surgery.scheduled_date|date:"d/m/Y H:i" }}</p>
                    
                    <p><strong>Theatre:</strong><br>
                    {{ object.surgery.theatre.name }}</p>
                    
                    <p><strong>Status:</strong><br>
                    {% if object.surgery.status == 'scheduled' %}
                        <span class="badge badge-primary">{{ object.surgery.get_status_display }}</span>
                    {% elif object.surgery.status == 'in_progress' %}
                        <span class="badge badge-warning">{{ object.surgery.get_status_display }}</span>
                    {% elif object.surgery.status == 'completed' %}
                        <span class="badge badge-success">{{ object.surgery.get_status_display }}</span>
                    {% elif object.surgery.status == 'cancelled' %}
                        <span class="badge badge-danger">{{ object.surgery.get_status_display }}</span>
                    {% else %}
                        <span class="badge badge-secondary">{{ object.surgery.get_status_display }}</span>
                    {% endif %}</p>
                    
                    <a href="{% url 'theatre:surgery_detail' object.surgery.pk %}" class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i> View Surgery Details
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="confirmDeleteModal" tabindex="-1" role="dialog" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmDeleteModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this team member assignment?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Delete confirmation
    $('.delete-confirm').on('click', function(e) {
        e.preventDefault();
        var targetUrl = $(this).attr('href');
        
        $('#confirmDeleteModal').modal('show');
        $('#confirmDeleteBtn').off('click').on('click', function() {
            window.location.href = targetUrl;
        });
    });
});
</script>
{% endblock %}
