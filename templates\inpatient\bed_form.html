{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.ward.id_for_label }}" class="form-label">Ward</label>
                                {{ form.ward|add_class:"form-select" }}
                                {% if form.ward.errors %}
                                    <div class="text-danger">
                                        {{ form.ward.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.bed_number.id_for_label }}" class="form-label">Bed Number</label>
                                {{ form.bed_number|add_class:"form-control" }}
                                {% if form.bed_number.errors %}
                                    <div class="text-danger">
                                        {{ form.bed_number.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                                {{ form.description|add_class:"form-control" }}
                                {% if form.description.errors %}
                                    <div class="text-danger">
                                        {{ form.description.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="form-group">
                                <div class="form-check">
                                    {{ form.is_active|add_class:"form-check-input" }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        Active
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="text-danger">
                                        {{ form.is_active.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between">
                                {% if bed %}
                                    <a href="{% url 'inpatient:ward_detail' bed.ward.id %}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i> Back to Ward
                                    </a>
                                {% else %}
                                    <a href="{% url 'inpatient:beds' %}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i> Back to Beds
                                    </a>
                                {% endif %}
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> Save Bed
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
