{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h5>Patient: {{ patient.get_full_name }}</h5>
                    <p class="mb-0"><strong>Patient ID:</strong> {{ patient.patient_id }}</p>
                </div>

                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.date.id_for_label }}" class="form-label">Date</label>
                            {{ form.date|add_class:"form-control" }}
                            {% if form.date.errors %}
                                <div class="text-danger">
                                    {{ form.date.errors }}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.doctor_name.id_for_label }}" class="form-label">Doctor</label>
                            {{ form.doctor_name|add_class:"form-control" }}
                            {% if form.doctor_name.errors %}
                                <div class="text-danger">
                                    {{ form.doctor_name.errors }}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.diagnosis.id_for_label }}" class="form-label">Diagnosis</label>
                            {{ form.diagnosis|add_class:"form-control" }}
                            {% if form.diagnosis.errors %}
                                <div class="text-danger">
                                    {{ form.diagnosis.errors }}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.treatment.id_for_label }}" class="form-label">Treatment</label>
                            {{ form.treatment|add_class:"form-control" }}
                            {% if form.treatment.errors %}
                                <div class="text-danger">
                                    {{ form.treatment.errors }}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                            {{ form.notes|add_class:"form-control" }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {{ form.notes.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'patients:medical_history' patient.id %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Medical History
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
