{% extends 'base.html' %}
{% block title %}Create Role - Hospital Management System{% endblock %}

{% block extra_css %}
<style>
    .permission-group {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }
    .permission-group-header {
        background-color: #f8f9fa;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #dee2e6;
        font-weight: 600;
    }
    .permission-list {
        padding: 1rem;
        max-height: 200px;
        overflow-y: auto;
    }
    .form-check {
        margin-bottom: 0.5rem;
    }
    .select-all-btn {
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-plus-circle me-2"></i>Create New Role</h2>
                <a href="{% url 'accounts:role_management' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Roles
                </a>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user-shield me-2"></i>Role Information</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        <i class="fas fa-tag me-1"></i>Role Name *
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.parent.id_for_label }}" class="form-label">
                                        <i class="fas fa-sitemap me-1"></i>Parent Role
                                    </label>
                                    {{ form.parent }}
                                    {% if form.parent.errors %}
                                        <div class="text-danger small">{{ form.parent.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">Select a parent role to inherit permissions from</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-1"></i>Description
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-key me-1"></i>Permissions
                            </label>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="text-muted">Select permissions for this role</span>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-primary select-all-btn" onclick="selectAllPermissions()">
                                        Select All
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary select-all-btn" onclick="deselectAllPermissions()">
                                        Deselect All
                                    </button>
                                </div>
                            </div>

                            <div id="permissions-container">
                                {% regroup form.permissions.field.queryset by content_type.app_label as app_groups %}
                                {% for app_group in app_groups %}
                                    <div class="permission-group">
                                        <div class="permission-group-header">
                                            <i class="fas fa-folder me-2"></i>{{ app_group.grouper|title }}
                                            <button type="button" class="btn btn-sm btn-outline-primary float-end select-all-btn" 
                                                    onclick="toggleAppPermissions('{{ app_group.grouper }}')">
                                                Toggle All
                                            </button>
                                        </div>
                                        <div class="permission-list">
                                            {% regroup app_group.list by content_type.model as model_groups %}
                                            {% for model_group in model_groups %}
                                                <div class="mb-3">
                                                    <h6 class="text-primary">{{ model_group.grouper|title }}</h6>
                                                    {% for permission in model_group.list %}
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox app-{{ app_group.grouper }}" 
                                                                   type="checkbox" 
                                                                   name="{{ form.permissions.name }}" 
                                                                   value="{{ permission.id }}" 
                                                                   id="permission_{{ permission.id }}">
                                                            <label class="form-check-label" for="permission_{{ permission.id }}">
                                                                {{ permission.name }}
                                                                <small class="text-muted">({{ permission.codename }})</small>
                                                            </label>
                                                        </div>
                                                    {% endfor %}
                                                </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                            {% if form.permissions.errors %}
                                <div class="text-danger small">{{ form.permissions.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'accounts:role_management' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Role
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function selectAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
        checkbox.checked = true;
    });
}

function deselectAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(function(checkbox) {
        checkbox.checked = false;
    });
}

function toggleAppPermissions(appName) {
    const checkboxes = document.querySelectorAll('.app-' + appName);
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(function(checkbox) {
        checkbox.checked = !allChecked;
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // Add form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const roleName = document.querySelector('#{{ form.name.id_for_label }}').value.trim();
        if (!roleName) {
            e.preventDefault();
            alert('Please enter a role name.');
            return;
        }
    });
});
</script>
{% endblock %}
