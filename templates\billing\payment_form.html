{% extends 'base.html' %}
{% load form_tags %}
{% load billing_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>Invoice Information</h5>
                        <p><strong>Invoice Number:</strong> {{ invoice.invoice_number }}</p>
                        <p><strong>Date:</strong> {{ invoice.created_at|date:"F d, Y" }}</p>
                        <p><strong>Due Date:</strong> {{ invoice.due_date|date:"F d, Y" }}</p>
                        <p><strong>Status:</strong> {{ invoice.status|payment_status_badge }}</p>
                    </div>
                    <div class="col-md-6">
                        <h5>Patient Information</h5>
                        <p><strong>Name:</strong> {{ invoice.patient.get_full_name }}</p>
                        <p><strong>Patient ID:</strong> {{ invoice.patient.patient_id }}</p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>Total Amount:</strong> {{ invoice.total_amount|currency }}
                                </div>
                                <div class="col-md-4">
                                    <strong>Amount Paid:</strong> {{ invoice.amount_paid|currency }}
                                </div>
                                <div class="col-md-4">
                                    <strong>Balance Due:</strong> {{ remaining_amount|currency }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <form method="post" id="payment-form">
                    {% csrf_token %}

                    <!-- Payment Source Selection -->
                    {% if form.payment_source %}
                    <div class="mb-3">
                        <label class="form-label">Payment Source</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_source" value="billing_office" id="billing_office" {% if form.payment_source.value == 'billing_office' or not form.payment_source.value %}checked{% endif %}>
                                    <label class="form-check-label" for="billing_office">
                                        <i class="fas fa-building"></i> Direct Billing
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_source" value="patient_wallet" id="patient_wallet" {% if form.payment_source.value == 'patient_wallet' %}checked{% endif %}>
                                    <label class="form-check-label" for="patient_wallet">
                                        <i class="fas fa-wallet"></i> Patient Wallet
                                        {% if patient_wallet %}
                                            <small class="text-muted d-block">Balance: {{ patient_wallet.balance|currency }}</small>
                                        {% endif %}
                                    </label>
                                </div>
                            </div>
                        </div>
                        {% if form.payment_source.errors %}
                            <div class="text-danger">{{ form.payment_source.errors }}</div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <div class="mb-3">
                        <label for="{{ form.amount.id_for_label }}" class="form-label">Payment Amount</label>
                        {{ form.amount|add_class:"form-control" }}
                        {% if form.amount.errors %}
                            <div class="text-danger">
                                {{ form.amount.errors }}
                            </div>
                        {% endif %}
                        <div class="form-text">Enter the amount being paid. Maximum amount is {{ remaining_amount|currency }}.</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3" id="payment-method-field">
                            <label for="{{ form.payment_method.id_for_label }}" class="form-label">Payment Method</label>
                            {{ form.payment_method|add_class:"form-control" }}
                            {% if form.payment_method.errors %}
                                <div class="text-danger">
                                    {{ form.payment_method.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="{{ form.payment_date.id_for_label }}" class="form-label">Payment Date</label>
                            {{ form.payment_date|add_class:"form-control datepicker" }}
                            {% if form.payment_date.errors %}
                                <div class="text-danger">
                                    {{ form.payment_date.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.transaction_id.id_for_label }}" class="form-label">Transaction ID</label>
                        {{ form.transaction_id|add_class:"form-control" }}
                        {% if form.transaction_id.errors %}
                            <div class="text-danger">
                                {{ form.transaction_id.errors }}
                            </div>
                        {% endif %}
                        <div class="form-text">For checks, card payments, or bank transfers, enter a transaction ID or reference number.</div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                        {{ form.notes|add_class:"form-control" }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {{ form.notes.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'billing:detail' invoice.id %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-money-bill me-1"></i> Record Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Handle payment source changes
document.addEventListener('DOMContentLoaded', function() {
    const paymentSourceRadios = document.querySelectorAll('input[name="payment_source"]');
    const paymentMethodField = document.getElementById('payment-method-field');
    const paymentMethodSelect = document.querySelector('select[name="payment_method"]');
    
    function updatePaymentMethod() {
        const selectedSource = document.querySelector('input[name="payment_source"]:checked');
        
        if (selectedSource && selectedSource.value === 'patient_wallet') {
            // Hide payment method field for wallet payments
            if (paymentMethodField) {
                paymentMethodField.style.display = 'none';
            }
            if (paymentMethodSelect) {
                paymentMethodSelect.value = 'wallet';
            }
        } else {
            // Show payment method field for direct billing
            if (paymentMethodField) {
                paymentMethodField.style.display = 'block';
            }
            if (paymentMethodSelect && paymentMethodSelect.value === 'wallet') {
                paymentMethodSelect.value = 'cash';
            }
        }
    }
    
    if (paymentSourceRadios.length > 0) {
        paymentSourceRadios.forEach(radio => {
            radio.addEventListener('change', updatePaymentMethod);
        });
        
        // Initialize on page load
        updatePaymentMethod();
    }
});
</script>
{% endblock %}
