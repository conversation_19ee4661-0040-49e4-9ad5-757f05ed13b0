# Generated by Django 5.2 on 2025-06-28 07:19

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pharmacy', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Dispensary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('location', models.Char<PERSON>ield(blank=True, max_length=200, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_dispensaries', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Dispensaries',
                'ordering': ['name'],
            },
        ),
    ]
