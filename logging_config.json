{"version": 1, "disable_existing_loggers": false, "formatters": {"verbose": {"format": "{levelname} {asctime} {module} {process:d} {thread:d} {message}", "style": "{"}, "simple": {"format": "{levelname} {message}", "style": "{"}}, "handlers": {"file_debug": {"level": "DEBUG", "class": "logging.handlers.RotatingFileHandler", "filename": "logs/hms_debug.log", "maxBytes": 10485760, "backupCount": 5, "formatter": "verbose"}, "file_error": {"level": "ERROR", "class": "logging.handlers.RotatingFileHandler", "filename": "logs/hms_errors.log", "maxBytes": 10485760, "backupCount": 5, "formatter": "verbose"}, "file_security": {"level": "INFO", "class": "logging.handlers.RotatingFileHandler", "filename": "logs/hms_security.log", "maxBytes": 10485760, "backupCount": 5, "formatter": "verbose"}, "console": {"level": "INFO", "class": "logging.StreamHandler", "formatter": "simple"}}, "loggers": {"django": {"handlers": ["file_debug", "console"], "level": "INFO", "propagate": false}, "hms": {"handlers": ["file_debug", "file_error", "console"], "level": "DEBUG", "propagate": false}, "security": {"handlers": ["file_security", "console"], "level": "INFO", "propagate": false}}, "root": {"handlers": ["file_debug", "console"], "level": "INFO"}}