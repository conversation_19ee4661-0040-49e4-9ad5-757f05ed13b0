{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Purchase Information</h6>
        </div>
        <div class="card-body">
            <p><strong>Invoice Number:</strong> {{ purchase.invoice_number }}</p>
            <p><strong>Supplier:</strong> {{ purchase.supplier.name }}</p>
            <p><strong>Purchase Date:</strong> {{ purchase.purchase_date }}</p>
            <p><strong>Total Amount:</strong> {{ purchase.total_amount }}</p>
            <p><strong>Payment Status:</strong> {{ purchase.get_payment_status_display }}</p>
            <p><strong>Dispensary:</strong> {{ purchase.dispensary.name|default:"N/A" }}</p>
            <p><strong>Notes:</strong> {{ purchase.notes|default:"N/A" }}</p>
            <p><strong>Created By:</strong> {{ purchase.created_by.get_full_name|default:"N/A" }}</p>
            <p><strong>Created At:</strong> {{ purchase.created_at|date:"Y-m-d H:i" }}</p>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Purchase Items</h6>
        </div>
        <div class="card-body">
            {% if purchase_items %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Medication</th>
                            <th>Quantity</th>
                            <th>Unit Price</th>
                            <th>Total Price</th>
                            <th>Batch Number</th>
                            <th>Expiry Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in purchase_items %}
                        <tr>
                            <td>{{ item.medication.name }} ({{ item.medication.strength }})</td>
                            <td>{{ item.quantity }}</td>
                            <td>{{ item.unit_price }}</td>
                            <td>{{ item.total_price }}</td>
                            <td>{{ item.batch_number|default:"N/A" }}</td>
                            <td>{{ item.expiry_date|date:"Y-m-d" }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p>No items for this purchase.</p>
            {% endif %}
        </div>
    </div>

    <div class="card-footer">
        <a href="{% url 'pharmacy:manage_purchases' %}" class="btn btn-secondary">Back to Purchases</a>
    </div>
</div>
{% endblock %}