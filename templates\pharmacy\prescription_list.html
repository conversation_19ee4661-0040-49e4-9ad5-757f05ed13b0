{% extends 'base.html' %}

{% block title %}Prescriptions - Hospital Management System{% endblock %}

{% block extra_css %}
<style>
    .search-form .form-group {
        margin-bottom: 1rem;
    }

    .search-form .btn {
        margin-right: 0.5rem;
    }

    .prescription-card {
        transition: transform 0.2s ease-in-out;
    }

    .prescription-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .form-control:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Prescriptions</h4>
                <a href="{% url 'pharmacy:create_prescription' %}" class="btn btn-light">
                    <i class="fas fa-plus"></i> Create Prescription
                </a>
            </div>
            <div class="card-body">
                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Prescriptions</h5>
                                <h2 class="mb-0">{{ total_prescriptions }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Pending</h5>
                                <h2 class="mb-0">{{ pending_count }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Processing</h5>
                                <h2 class="mb-0">{{ processing_count }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Completed</h5>
                                <h2 class="mb-0">{{ completed_count }}</h2>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Search and Filter Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-search"></i> Search & Filter Prescriptions
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="get" class="search-form">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="{{ form.search.id_for_label }}" class="form-label">{{ form.search.label }}</label>
                                        {{ form.search }}
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="{{ form.patient_number.id_for_label }}" class="form-label">{{ form.patient_number.label }}</label>
                                        {{ form.patient_number }}
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="{{ form.medication_name.id_for_label }}" class="form-label">{{ form.medication_name.label }}</label>
                                        {{ form.medication_name }}
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="{{ form.doctor.id_for_label }}" class="form-label">{{ form.doctor.label }}</label>
                                        {{ form.doctor }}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                                        {{ form.status }}
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="{{ form.payment_status.id_for_label }}" class="form-label">{{ form.payment_status.label }}</label>
                                        {{ form.payment_status }}
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="{{ form.date_from.id_for_label }}" class="form-label">{{ form.date_from.label }}</label>
                                        {{ form.date_from }}
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="{{ form.date_to.id_for_label }}" class="form-label">{{ form.date_to.label }}</label>
                                        {{ form.date_to }}
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <div class="form-group w-100">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                        <a href="{% url 'pharmacy:prescriptions' %}" class="btn btn-secondary">
                                            <i class="fas fa-redo"></i> Reset
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Prescriptions Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>Patient</th>
                                <th>Doctor</th>
                                <th>Diagnosis</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for prescription in page_obj %}
                                <tr class="{% if prescription.status == 'pending' %}table-warning{% elif prescription.status == 'processing' %}table-info{% elif prescription.status == 'completed' %}table-success{% elif prescription.status == 'cancelled' %}table-danger{% endif %}">
                                    <td>{{ prescription.id }}</td>
                                    <td>{{ prescription.prescription_date|date:"M d, Y" }}</td>
                                    <td>
                                        <a href="{% url 'patients:detail' prescription.patient.id %}">
                                            {{ prescription.patient.get_full_name }}
                                        </a>
                                    </td>
                                    <td>Dr. {{ prescription.doctor.get_full_name }}</td>
                                    <td>{{ prescription.diagnosis|truncatechars:30 }}</td>
                                    <td>
                                        {% if prescription.status == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif prescription.status == 'processing' %}
                                            <span class="badge bg-info">Processing</span>
                                        {% elif prescription.status == 'completed' %}
                                            <span class="badge bg-success">Completed</span>
                                        {% elif prescription.status == 'cancelled' %}
                                            <span class="badge bg-danger">Cancelled</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if prescription.status != 'completed' and prescription.status != 'cancelled' %}
                                                <a href="{% url 'pharmacy:dispense_prescription' prescription.id %}" class="btn btn-success" title="Dispense">
                                                    <i class="fas fa-prescription-bottle-alt"></i>
                                                </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center">No prescriptions found.</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize select2 for doctor dropdown if available
    if (typeof $ !== 'undefined' && $.fn.select2) {
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    }

    // Enhanced search form functionality
    const searchForm = document.querySelector('form[method="get"]');
    const searchInput = document.querySelector('input[name="search"]');
    const statusSelect = document.querySelector('select[name="status"]');
    const paymentStatusSelect = document.querySelector('select[name="payment_status"]');

    if (searchForm) {
        // Add loading state to search button
        const searchButton = searchForm.querySelector('button[type="submit"]');
        const originalButtonText = searchButton.innerHTML;

        searchForm.addEventListener('submit', function() {
            searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
            searchButton.disabled = true;
        });

        // Add search input enhancements
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                // Clear previous timeout
                clearTimeout(searchTimeout);

                // Add visual feedback
                if (this.value.length > 0) {
                    this.classList.add('border-primary');
                } else {
                    this.classList.remove('border-primary');
                }
            });
        }
    }

    // Add tooltips to action buttons
    const actionButtons = document.querySelectorAll('[title]');
    actionButtons.forEach(button => {
        button.setAttribute('data-toggle', 'tooltip');
        button.setAttribute('data-placement', 'top');
    });

    // Initialize tooltips if Bootstrap is available
    if (typeof $ !== 'undefined' && $.fn.tooltip) {
        $('[data-toggle="tooltip"]').tooltip();
    }

    // Add confirmation for delete actions
    const deleteButtons = document.querySelectorAll('a[href*="delete"]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this prescription?')) {
                e.preventDefault();
            }
        });
    });

    // Highlight search terms in results
    const searchTerm = new URLSearchParams(window.location.search).get('search');
    if (searchTerm && searchTerm.length > 0) {
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            cells.forEach(cell => {
                const text = cell.textContent;
                if (text.toLowerCase().includes(searchTerm.toLowerCase())) {
                    const regex = new RegExp(`(${searchTerm})`, 'gi');
                    cell.innerHTML = cell.innerHTML.replace(regex, '<mark>$1</mark>');
                }
            });
        });
    }
});
</script>
{% endblock %}
