{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-2 text-gray-800">{{ title }}</h1>
    <p class="mb-4">A list of all staff schedules in the system.</p>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="m-0 font-weight-bold text-primary">{{ total_schedules }} Schedules</h6>
                </div>
                <div class="col-md-6 text-right">
                    <a href="{% url 'hr:create_schedule' %}" class="btn btn-primary btn-sm">Create Schedule</a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form method="get" class="form-inline mb-3">
                <input type="text" name="search" class="form-control mr-sm-2" placeholder="Search schedules..." value="{{ search_query }}">
                <button type="submit" class="btn btn-primary">Search</button>
            </form>

            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Staff</th>
                            <th>Weekday</th>
                            <th>Shift</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for schedule in page_obj %}
                        <tr>
                            <td>{{ schedule.staff.get_full_name }}</td>
                            <td>{{ schedule.get_weekday_display }}</td>
                            <td>{{ schedule.shift.name }}</td>
                            <td>{% if schedule.is_active %}<span class="badge badge-success">Active</span>{% else %}<span class="badge badge-danger">Inactive</span>{% endif %}</td>
                            <td>
                                <a href="{% url 'hr:edit_schedule' schedule.id %}" class="btn btn-warning btn-sm">Edit</a>
                                <a href="{% url 'hr:delete_schedule' schedule.id %}" class="btn btn-danger btn-sm">Delete</a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5">No schedules found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                    <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">&laquo;</a></li>
                    {% else %}
                    <li class="page-item disabled"><a class="page-link" href="#">&laquo;</a></li>
                    {% endif %}

                    {% for i in page_obj.paginator.page_range %}
                    {% if page_obj.number == i %}
                    <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                    {% else %}
                    <li class="page-item"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">&raquo;</a></li>
                    {% else %}
                    <li class="page-item disabled"><a class="page-link" href="#">&raquo;</a></li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}