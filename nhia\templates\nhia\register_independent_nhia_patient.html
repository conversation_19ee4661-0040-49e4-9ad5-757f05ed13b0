{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{% url 'dashboard:dashboard' %}">Dashboard</a></li>
<li class="breadcrumb-item"><a href="{% url 'nhia:nhia_patient_list' %}">NHIA Patients</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ title }}</li>
{% endblock breadcrumbs %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{{ title }}</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-3">
                        <strong>Note:</strong> This form registers a patient as an <span class="badge bg-success">Independent NHIA Patient</span>.<br>
                        The patient will be assigned a unique NHIA Patient ID and type.
                    </div>
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        {{ form|crispy }}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <button type="submit" class="btn btn-primary me-md-2">Register NHIA Patient</button>
                            <a href="{% url 'nhia:nhia_patient_list' %}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}