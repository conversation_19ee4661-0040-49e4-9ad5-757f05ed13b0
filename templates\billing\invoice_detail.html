{% extends 'base.html' %}
{% load billing_tags %}
{% load form_tags %}

{% block title %}Invoice #{{ invoice.invoice_number }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Invoice #{{ invoice.invoice_number }}</h4>
                <div>
                    <a href="{% url 'billing:print' invoice.id %}" class="btn btn-light me-2" target="_blank">
                        <i class="fas fa-print me-1"></i> Print
                    </a>
                    <a href="{% url 'billing:list' %}" class="btn btn-light">
                        <i class="fas fa-arrow-left me-1"></i> Back to Invoices
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>Invoice Information</h5>
                        <p><strong>Invoice Number:</strong> {{ invoice.invoice_number }}</p>
                        <p><strong>Date:</strong> {{ invoice.created_at|date:"F d, Y" }}</p>
                        <p><strong>Due Date:</strong> {{ invoice.due_date|date:"F d, Y" }}</p>
                        <p><strong>Status:</strong> {{ invoice.status|payment_status_badge }}</p>
                        <p><strong>Created By:</strong> {{ invoice.created_by.get_full_name }}</p>
                    </div>
                    <div class="col-md-6">
                        <h5>Patient Information</h5>
                        <p><strong>Name:</strong> <a href="{% url 'patients:detail' invoice.patient.id %}">{{ invoice.patient.get_full_name }}</a></p>
                        <p><strong>Patient ID:</strong> {{ invoice.patient.patient_id }}</p>
                        <p><strong>Phone:</strong> {{ invoice.patient.phone_number }}</p>
                        <p><strong>Email:</strong> {{ invoice.patient.email }}</p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5>Notes</h5>
                        <p>{{ invoice.notes|default:"No notes provided." }}</p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Invoice Items</h5>
                                {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addItemModal" id="addItemModalButton">
                                        <i class="fas fa-plus-circle me-1"></i> Add Item
                                    </button>
                                {% endif %}
                            </div>
                            <div class="card-body">
                                {% if invoice_items %}
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Service</th>
                                                    <th>Description</th>
                                                    <th class="text-center">Quantity</th>
                                                    <th class="text-end">Unit Price</th>
                                                    <th class="text-end">Total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for item in invoice_items %}
                                                    <tr>
                                                        <td>
                                                            {% if item.service %}
                                                                {{ item.service.name }}
                                                            {% else %}
                                                                -
                                                            {% endif %}
                                                        </td>
                                                        <td>{{ item.description }}</td>
                                                        <td class="text-center">{{ item.quantity }}</td>
                                                        <td class="text-end">₦{{ item.unit_price|floatformat:2 }}</td>
                                                        <td class="text-end">₦{{ item.total_price|floatformat:2 }}</td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th colspan="4" class="text-end">Subtotal:</th>
                                                    <th class="text-end">₦{{ invoice.subtotal|floatformat:2 }}</th>
                                                </tr>
                                                {% if invoice.tax_amount > 0 %}
                                                <tr>
                                                    <th colspan="4" class="text-end">Tax:</th>
                                                    <th class="text-end">₦{{ invoice.tax_amount|floatformat:2 }}</th>
                                                </tr>
                                                {% endif %}
                                                {% if invoice.discount_amount > 0 %}
                                                <tr>
                                                    <th colspan="4" class="text-end">Discount:</th>
                                                    <th class="text-end">-₦{{ invoice.discount_amount|floatformat:2 }}</th>
                                                </tr>
                                                {% endif %}
                                                <tr>
                                                    <th colspan="4" class="text-end">Total:</th>
                                                    <th class="text-end">₦{{ invoice.total_amount|floatformat:2 }}</th>
                                                </tr>
                                                <tr>
                                                    <th colspan="4" class="text-end">Amount Paid:</th>
                                                    <th class="text-end">₦{{ invoice.amount_paid|floatformat:2 }}</th>
                                                </tr>
                                                <tr>
                                                    <th colspan="4" class="text-end">Balance Due:</th>
                                                    <th class="text-end">₦{{ invoice.get_balance|floatformat:2 }}</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No items have been added to this invoice yet.
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Payments</h5>
                                {% if invoice.status != 'paid' and invoice.status != 'cancelled' and remaining_amount > 0 %}
                                    <a href="{% url 'billing:record_payment' invoice.id %}" class="btn btn-success btn-sm">
                                        <i class="fas fa-money-bill me-1"></i> Record Payment
                                    </a>
                                {% endif %}
                            </div>
                            <div class="card-body">
                                {% if payments %}
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Amount</th>
                                                    <th>Method</th>
                                                    <th>Reference</th>
                                                    <th>Received By</th>
                                                    <th>Notes</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for payment in payments %}
                                                    <tr>
                                                        <td>{{ payment.payment_date|date:"M d, Y" }}</td>
                                                        <td>₦{{ payment.amount|floatformat:2 }}</td>
                                                        <td>{{ payment.get_payment_method_display }}</td>
                                                        <td>{{ payment.reference_number|default:"-" }}</td>
                                                        <td>{% if payment.received_by %}{{ payment.received_by.get_full_name|default:payment.received_by.username }}{% else %}-{% endif %}</td>
                                                        <td>{{ payment.notes|default:"-" }}</td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th colspan="5" class="text-end">Total Paid:</th>
                                                    <th>{% if invoice.amount_paid %}₦{{ invoice.amount_paid|floatformat:2 }}{% else %}₦0.00{% endif %}</th>
                                                </tr>
                                            </tfoot>
                                    </div>
                                {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No payments have been recorded for this invoice yet.
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{% url 'billing:list' %}" class="btn btn-secondary me-md-2">
                        <i class="fas fa-arrow-left me-1"></i> Back to Invoices
                    </a>
                    {% if invoice.status != 'paid' and invoice.status != 'cancelled' and invoice.get_balance > 0 %}
                        <a href="{% url 'billing:payment' invoice.id %}" class="btn btn-success me-md-2">
                            <i class="fas fa-money-bill me-1"></i> Record Payment
                        </a>
                    {% endif %}
                    {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                        <a href="{% url 'billing:edit' invoice.id %}" class="btn btn-primary me-md-2">
                            <i class="fas fa-edit me-1"></i> Edit Invoice
                        </a>

                        <a href="{% url 'billing:delete' invoice.id %}" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> Delete Invoice
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Select2 initialization
        $('.select2-modal').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dropdownParent: $('#addItemModal')
        });

        // Update description and unit price when service is selected
        $('#{{ item_form.service.id_for_label }}').on('change', function() {
            var serviceId = $(this).val();
            if (serviceId) {
                $.ajax({
                    url: '/billing/api/services/' + serviceId + '/',
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        $('#{{ item_form.description.id_for_label }}').val(data.name);
                        $('#{{ item_form.unit_price.id_for_label }}').val(data.price);
                    }
                });
            }
        });

        // Explicitly initialize and show the modal when the button is clicked
        var addItemModalElement = document.getElementById('addItemModal');
        var addItemButton = document.getElementById('addItemModalButton');

        if (addItemModalElement && addItemButton) {
            var addItemModal = new bootstrap.Modal(addItemModalElement);
            addItemButton.addEventListener('click', function() {
                console.log("Add Item button clicked. Attempting to show modal.");
                addItemModal.show();
            });
        } else {
            console.error("Could not find modal element or button element.");
        }
    });
</script>
{% endblock %}
