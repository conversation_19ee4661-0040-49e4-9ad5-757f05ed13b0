{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Manage Experience - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Manage Work Experience</h1>
        <a href="{% url 'doctors:doctor_profile' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-1"></i> Back to Profile
        </a>
    </div>

    <div class="row">
        <!-- Add Experience Form -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Add Work Experience</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.hospital_name.id_for_label }}" class="form-label">Hospital/Organization *</label>
                            {{ form.hospital_name|add_class:"form-control" }}
                            {% if form.hospital_name.errors %}
                                <div class="text-danger">{{ form.hospital_name.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.position.id_for_label }}" class="form-label">Position *</label>
                            {{ form.position|add_class:"form-control" }}
                            {% if form.position.errors %}
                                <div class="text-danger">{{ form.position.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.start_date.id_for_label }}" class="form-label">Start Date *</label>
                            {{ form.start_date|add_class:"form-control" }}
                            {% if form.start_date.errors %}
                                <div class="text-danger">{{ form.start_date.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.end_date.id_for_label }}" class="form-label">End Date</label>
                            {{ form.end_date|add_class:"form-control" }}
                            <small class="form-text text-muted">{{ form.end_date.help_text }}</small>
                            {% if form.end_date.errors %}
                                <div class="text-danger">{{ form.end_date.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            {{ form.description|add_class:"form-control" }}
                            {% if form.description.errors %}
                                <div class="text-danger">{{ form.description.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus-circle mr-1"></i> Add Experience
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Experience List -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Work Experience</h6>
                </div>
                <div class="card-body">
                    {% if experience %}
                        <div class="table-responsive">
                            <table class="table table-bordered" id="experienceTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Position</th>
                                        <th>Hospital/Organization</th>
                                        <th>Duration</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for exp in experience %}
                                        <tr>
                                            <td>{{ exp.position }}</td>
                                            <td>{{ exp.hospital_name }}</td>
                                            <td>
                                                {{ exp.start_date|date:"M Y" }} - 
                                                {% if exp.end_date %}
                                                    {{ exp.end_date|date:"M Y" }}
                                                {% else %}
                                                    Present
                                                {% endif %}
                                            </td>
                                            <td>{{ exp.description|default:"-"|truncatechars:50 }}</td>
                                            <td>
                                                <form method="post" action="{% url 'doctors:delete_experience' exp.id %}">
                                                    {% csrf_token %}
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this experience entry?');">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No work experience found.</p>
                            <p class="text-muted">Add your work experience to enhance your profile.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#experienceTable').DataTable({
            "order": [[2, "desc"]]
        });
    });
</script>
{% endblock %}
